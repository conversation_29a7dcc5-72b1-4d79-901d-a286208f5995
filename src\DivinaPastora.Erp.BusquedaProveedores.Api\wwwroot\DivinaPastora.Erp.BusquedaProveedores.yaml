openapi: 3.0.1
info:
  title: Busqueda Proveedores
  description: Microservicio de búsqueda de servicios/proveedores
  version: 0.1.0
servers:
  - url: /
paths:
  /cuadros:
    get:
      tags:
        - Busqueda
      summary: Obtiene una lista de cuadros de servicios/proveedores
      x-audit-ignore: true
      operationId: GetCuadros
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Cuadro'
                x-content-type: application/json
              x-content-type: application/json
  /categorias/{cuadroId}:
    get:
      tags:
        - Busqueda
      summary: Obtiene una lista de categorías de servicios/proveedores
      x-audit-ignore: true
      operationId: GetCategorias
      parameters:
        - name: cuadroId
          in: path
          description: ID del cuadro al que pertenecen las categorías
          required: true
          schema:
            type: integer
          
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Categoria'
                x-content-type: application/json
              x-content-type: application/json
  /busqueda:
    get:
      tags:
        - Busqueda
      summary: Obtiene una lista de servicios/proveedores en función de los parámetros de búsqueda
      x-audit-ignore: true
      operationId: Busqueda
      parameters:
        - name: limit
          in: query
          description: Máximo número de elementos de la respuesta
          required: false
          schema:
            type: integer
            nullable: true
            default: 15
        - name: offset
          in: query
          description: Número de elementos que no se incluirán en la respuesta (número de página)
          required: false
          schema:
            type: integer
            nullable: true
            default: 0
        - name: cuadroId
          in: query
          description: Id del cuadro en el que se realiza la búsqueda
          required: true
          schema:
            type: integer
        - name: productoId
          in: query
          description: Id del producto sobre el que se realiza la búsqueda
          required: true
          schema:
            type: integer
        - name: categoriaId
          in: query
          description: Id de la categoria sobre el que se realiza la búsqueda
          required: true
          schema:
            type: integer
        - name: nombre
          in: query
          description: Nombre del producto/servicio a buscar
          required: false
          schema:
            type: string
        - name: provincia
          in: query
          description: Provincia del producto/servicio a buscar
          required: false
          schema:
            type: string
        - name: poblacion
          in: query
          description: Población del producto/servicio a buscar
          required: false
          schema:
            type: string
        - name: codigoPostal
          in: query
          description: Código Postal del producto/servicio a buscar
          required: false
          schema:
            type: string
        - name: direccion
          in: query
          description: Dirección del producto/servicio a buscar
          required: false
          schema:
            type: string
        - name: latitudSurOeste
          in: query
          description: Latitud de la esquina inferior izquierda de la ventana de búsqueda de productos/servicios
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: longitudSurOeste
          in: query
          description: Longitud de la esquina inferior izquierda de la ventana de búsqueda de productos/servicios
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: latitudNorEste
          in: query
          description: Latitud de la esquina superior derecha de la ventana de búsqueda de productos/servicios
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: longitudNorEste
          in: query
          description: Longitud de la esquina superior derecha de la ventana de búsqueda de productos/servicios
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: latitud
          in: query
          description: Latitud de la consulta, para ordenar los resultados por distancia
          required: false
          schema:
            type: number
            format: double
            nullable: true
        - name: longitud
          in: query
          description: Longitud de la consulta, para ordenar los resultados por distancia
          required: false
          schema:
            type: number
            format: double
            nullable: true
     
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/ResultadoBusqueda'
                x-content-type: application/json
              x-content-type: application/json               

components:
  schemas:
    Cuadro:
      type: object
      properties:
        id:
          type: integer
        nombreId:
          type: integer
      example:
        id: 1
        nombreId: 123
    Categoria:
      type: object
      properties:
        id:
          type: integer
        cuadroId:
          type: integer
        nombreId:
          type: integer
      example:
        id: 2
        cuadroId: 1
        nombreId: 456
    ResultadoBusqueda:
      type: object
      properties:
        totalServicios:
          type: integer
        servicios:
          type: array
          items:
            $ref: '#/components/schemas/Servicio'
    Servicio:
      type: object
      properties:
        id:
          type: integer
        cuadroId:
          type: integer
        productoId:
          type: integer
        categoriaId:
          type: integer          
        nombre:
          type: string
        provincia:
          type: string
        localidad:
          type: string
        codigoPostal:
          type: string
        ruta:
          type: string
        numero:
          type: string
        datosAdicionales:
          type: string
        telefono:
          type: string
        latitud:
          type: number
          format: double
          nullable: true
        longitud:
          type: number
          format: double
          nullable: true
        distancia:
          type: number
          format: double
          nullable: true
        prioridad:
          type: integer
      example:
        id: 2
        cuadroId: 1
        categoriaId: 13
        nombre: "Ejemplo de Servicio"
        provincia: "Valencia"
        localidad: "Castellón"
        codigoPostal: "45005"
        ruta: "Calle de ejemplo"
        numero: "16 bis"
        datosAdicionales: "puerta 7"
        telefono: "931111111"
        latitud: 41.48737
        longitud: 2.03991
        distancia: 12313.43
        prioridad: 0
        





