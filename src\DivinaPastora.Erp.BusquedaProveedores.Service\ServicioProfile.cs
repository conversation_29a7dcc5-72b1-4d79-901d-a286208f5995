﻿using DivinaPastora.Erp.BusquedaProveedores.Model;
using Data = DivinaPastora.Erp.BusquedaProveedores.Repository.Data;

namespace DivinaPastora.Erp.BusquedaProveedores.Service
{
    public class ServicioProfile : AutoMapper.Profile
    {
        public ServicioProfile()
        {
            CreateMap<Data.Servicio, Servicio>()
                .ForMember(
                    dest => dest.Latitud,
                    opt => opt.MapFrom(src => src.Coordenadas.Y))
                .ForMember(
                    dest => dest.Longitud,
                    opt => opt.MapFrom(src => src.Coordenadas.X));
        }
    }
}
