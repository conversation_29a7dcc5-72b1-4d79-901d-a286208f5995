<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Description>DivinaPastora.Erp.BusquedaProveedores.Service</Description>
    <Copyright>DivinaPastora.Erp.BusquedaProveedores.Service</Copyright>
    <TargetFramework>net6.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>DivinaPastora.Erp.BusquedaProveedores.Service</AssemblyName>
    <PackageId>DivinaPastora.Erp.BusquedaProveedores.Service</PackageId>
  </PropertyGroup>
  <PropertyGroup>
    <CodeAnalysisRuleSet>..\..\StyleCopRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\..\stylecop.json" Link="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" />
    <PackageReference Include="IdentityModel" Version="4.4.0" />
    <PackageReference Include="NLog" Version="4.7.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Model\DivinaPastora.Erp.BusquedaProveedores.Model.csproj" />
    <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Repository\DivinaPastora.Erp.BusquedaProveedores.Repository.csproj" />
  </ItemGroup>
</Project>
