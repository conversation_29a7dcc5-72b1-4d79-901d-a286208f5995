﻿using DivinaPastora.Erp.BusquedaProveedores.Model.Authentication;
using Microsoft.AspNetCore.Http;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares
{
    public partial class AuthenticationMiddleware
    {
        public void LoadAuthDataCustom(HttpContext httpContext, IAuthenticationData authenticationData)
        {
            // TODO: A completar por el usuario...
            /* Ej.
            var personaId = httpContext.User.Claims.SingleOrDefault(x => x.Type == "persona_id")?.Value;
            if (!string.IsNullOrEmpty(personaId))
            {
                authenticationData.SetPersonaId(int.Parse(personaId));
            }
            */
        }
    }
}
