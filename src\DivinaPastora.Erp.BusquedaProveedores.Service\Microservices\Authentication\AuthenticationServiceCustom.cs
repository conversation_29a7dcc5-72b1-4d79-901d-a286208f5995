﻿namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Authentication
{
    public partial class AuthenticationService : IAuthenticationService
    {
        // TODO: A completar por el usuario...
        /* Ej.
        public void SetTokenForScope<T>(T api, string scope)
        {
            switch (api)
            {
                case PolizaApi x:
                    SetAuthToken(x.ApiClient, scope);
                    break;
            }
        }

        private void SetAuthToken(ApiCore.ApiClient apiClient, string scope)
        {
            if (!apiClient.DefaultHeader.ContainsKey("Authorization"))
            {
                lock (apiClient)
                {
                    if (!apiClient.DefaultHeader.ContainsKey("Authorization"))
                    {
                        apiClient.AddDefaultHeader("Authorization", $"Bearer {GetTokenForScope(scope)}");
                    }
                }
            }
        }
        */
    }
}