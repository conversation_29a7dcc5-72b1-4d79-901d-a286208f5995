<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="StyleCopeRules" Description="StyleCopeRules custom ruleset" ToolsVersion="15.0">
  <IncludeAll Action="Warning" />
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CS1591" Action="None" />
    <Rule Id="SA1629" Action="None" />
  </Rules>
  <Rules AnalyzerId="StyleCop.Analyzers" RuleNamespace="StyleCop.Analyzers">
    <!-- SpecialRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/SpecialRules.md -->
    <Rule Id="SA0001" Action="None" /> <!-- XmlCommentAnalysisDisabled: All diagnostics of XML documentation comments has been disabled due to the current project configuration. -->
    <Rule Id="SA0002" Action="Error" /> <!-- InvalidSettingsFile: The stylecop.json settings file could not be loaded due to a deserialization error. -->

    <!-- SpacingRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/SpacingRules.md -->
    <Rule Id="SA1000" Action="Error" /> <!-- KeywordsMustBeSpacedCorrectly: The spacing around a C# keyword is incorrect. -->
    <Rule Id="SA1001" Action="Error" /> <!-- CommasMustBeSpacedCorrectly: The spacing around a comma is incorrect, within a C# code file. -->
    <Rule Id="SA1002" Action="Error" /> <!-- SemicolonsMustBeSpacedCorrectly: The spacing around a semicolon is incorrect, within a C# code file. -->
    <Rule Id="SA1003" Action="Error" /> <!-- SymbolsMustBeSpacedCorrectly: The spacing around an operator symbol is incorrect, within a C# code file. -->
    <Rule Id="SA1004" Action="Error" /> <!-- DocumentationLinesMustBeginWithSingleSpace: A line within a documentation header above a C# element does not begin with a single space. -->
    <Rule Id="SA1005" Action="Error" /> <!-- SingleLineCommentsMustBeginWithSingleSpace: A single-line comment within a C# code file does not begin with a single space. -->
    <Rule Id="SA1006" Action="Error" /> <!-- PreprocessorKeywordsMustNotBePrecededBySpace: A C# preprocessor-type keyword is preceded by space. -->
    <Rule Id="SA1007" Action="Error" /> <!-- OperatorKeywordMustBeFollowedBySpace: The operator keyword within a C# operator overload method is not followed by any whitespace. -->
    <Rule Id="SA1008" Action="Error" /> <!-- OpeningParenthesisMustBeSpacedCorrectly: An opening parenthesis within a C# statement is not spaced correctly. -->
    <Rule Id="SA1009" Action="Error" /> <!-- ClosingParenthesisMustBeSpacedCorrectly: A closing parenthesis within a C# statement is not spaced correctly. -->
    <Rule Id="SA1010" Action="Error" /> <!-- OpeningSquareBracketsMustBeSpacedCorrectly: An opening square bracket within a C# statement is not spaced correctly. -->
    <Rule Id="SA1011" Action="Error" /> <!-- ClosingSquareBracketsMustBeSpacedCorrectly: A closing square bracket within a C# statement is not spaced correctly. -->
    <Rule Id="SA1012" Action="Error" /> <!-- OpeningBracesMustBeSpacedCorrectly: An opening brace within a C# element is not spaced correctly. -->
    <Rule Id="SA1013" Action="Error" /> <!-- ClosingBracesMustBeSpacedCorrectly: A closing brace within a C# element is not spaced correctly. -->
    <Rule Id="SA1014" Action="Error" /> <!-- OpeningGenericBracketsMustBeSpacedCorrectly: An opening generic bracket within a C# element is not spaced correctly. -->
    <Rule Id="SA1015" Action="Error" /> <!-- ClosingGenericBracketsMustBeSpacedCorrectly: A closing generic bracket within a C# element is not spaced correctly. -->
    <Rule Id="SA1016" Action="Error" /> <!-- OpeningAttributeBracketsMustBeSpacedCorrectly: An opening attribute bracket within a C# element is not spaced correctly. -->
    <Rule Id="SA1017" Action="Error" /> <!-- ClosingAttributeBracketsMustBeSpacedCorrectly: A closing attribute bracket within a C# element is not spaced correctly. -->
    <Rule Id="SA1018" Action="Error" /> <!-- NullableTypeSymbolsMustNotBePrecededBySpace: A nullable type symbol within a C# element is not spaced correctly. -->
    <Rule Id="SA1019" Action="Error" /> <!-- MemberAccessSymbolsMustBeSpacedCorrectly: The spacing around a member access symbol is incorrect, within a C# code file. -->
    <Rule Id="SA1020" Action="Error" /> <!-- IncrementDecrementSymbolsMustBeSpacedCorrectly: An increment or decrement symbol within a C# element is not spaced correctly. -->
    <Rule Id="SA1021" Action="Error" /> <!-- NegativeSignsMustBeSpacedCorrectly: A negative sign within a C# element is not spaced correctly. -->
    <Rule Id="SA1022" Action="Error" /> <!-- PositiveSignsMustBeSpacedCorrectly: A positive sign within a C# element is not spaced correctly. -->
    <Rule Id="SA1023" Action="Error" /> <!-- DereferenceAndAccessOfMustBeSpacedCorrectly: A dereference symbol or an access-of symbol within a C# element is not spaced correctly. -->
    <Rule Id="SA1024" Action="Error" /> <!-- ColonsMustBeSpacedCorrectly: A colon within a C# element is not spaced correctly. -->
    <Rule Id="SA1025" Action="Error" /> <!-- CodeMustNotContainMultipleWhitespaceInARow: The code contains multiple whitespace characters in a row. -->
    <Rule Id="SA1026" Action="Error" /> <!-- CodeMustNotContainSpaceAfterNewKeywordInImplicitlyTypedArrayAllocation: An implicitly typed new array allocation within a C# code file is not spaced correctly. -->
    <Rule Id="SA1027" Action="Error" /> <!-- UseTabsCorrectly: The code contains a tab or space character which is not consistent with the current project settings. -->
    <Rule Id="SA1028" Action="Error" /> <!-- CodeMustNotContainTrailingWhitespace: A line of code ends with a space, tab, or other whitespace characters before the end of line character(s). -->

    <!-- ReadabilityRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/ReadabilityRules.md -->
    <Rule Id="SA1100" Action="Error" /> <!-- DoNotPrefixCallsWithBaseUnlessLocalImplementationExists: A call to a member from an inherited class begins with base., and the local class does not contain an override or implementation of the member. -->
    <Rule Id="SA1101" Action="None" /> <!-- PrefixLocalCallsWithThis: A call to an instance member of the local class or a base class is not prefixed with "this.", within a C# code file. -->
    <Rule Id="SA1102" Action="Error" /> <!-- QueryClauses: A C# query clause does not begin on the same line as the previous clause, or on the next line. -->
    <Rule Id="SA1103" Action="Error" /> <!-- QueryClauses: The clauses within a C# query expression are not all placed on the same line, and each clause is not placed on its own line. -->
    <Rule Id="SA1104" Action="Error" /> <!-- QueryClauses: A clause within a C# query expression begins on the same line as the previous clause, when the previous clause spans across multiple lines. -->
    <Rule Id="SA1105" Action="Error" /> <!-- QueryClauses: A clause within a C# query expression spans across multiple lines, and does not begin on its own line. -->
    <Rule Id="SA1106" Action="Error" /> <!-- CodeMustNotContainEmptyStatements: The C# code contains an extra semicolon. -->
    <Rule Id="SA1107" Action="Error" /> <!-- CodeMustNotContainMultipleStatementsOnOneLine: The C# code contains more than one statement on a single line. -->
    <Rule Id="SA1108" Action="Error" /> <!-- BlockStatementsMustNotContainEmbeddedComments: A C# statement contains a comment between the declaration of the statement and the opening brace of the statement. -->
    <Rule Id="SA1109" Action="Error" /> <!-- BlockStatementsMustNotContainEmbeddedRegions: A C# statement contains a region tag between the declaration of the statement and the opening brace of the statement. -->
    <Rule Id="SA1110" Action="Error" /> <!-- OpeningParenthesisMustBeOnDeclarationLine: The opening parenthesis or bracket in a call to a C# method or indexer, or the declaration of a method or indexer, is not placed on the same line as the method or indexer name. -->
    <Rule Id="SA1111" Action="Error" /> <!-- ClosingParenthesisMustBeOnLineOfLastParameter: The closing parenthesis or bracket in a call to a C# method or indexer, or the declaration of a method or indexer, is not placed on the same line as the last parameter. -->
    <Rule Id="SA1112" Action="Error" /> <!-- ClosingParenthesisMustBeOnLineOfOpeningParenthesis: The closing parenthesis or bracket in a call to a C# method or indexer, or the declaration of a method or indexer, is not placed on the same line as the opening bracket when the element does not take any parameters. -->
    <Rule Id="SA1113" Action="Error" /> <!-- CommaMustBeOnSameLineAsPreviousParameter: A comma between two parameters in a call to a C# method or indexer, or in the declaration of a method or indexer, is not placed on the same line as the previous parameter. -->
    <Rule Id="SA1114" Action="Error" /> <!-- ParameterListMustFollowDeclaration: The start of the parameter list for a method or indexer call or declaration does not begin on the same line as the opening bracket, or on the line after the opening bracket. -->
    <Rule Id="SA1115" Action="Error" /> <!-- ParameterMustFollowComma: A parameter within a C# method or indexer call or declaration does not begin on the same line as the previous parameter, or on the next line. -->
    <Rule Id="SA1116" Action="Error" /> <!-- SplitParametersMustStartOnLineAfterDeclaration: The parameters to a C# method or indexer call or declaration span across multiple lines, but the first parameter does not start on the line after the opening bracket. -->
    <Rule Id="SA1117" Action="Error" /> <!-- ParametersMustBeOnSameLineOrSeparateLines: The parameters to a C# method or indexer call or declaration are not all on the same line or each on a separate line. -->
    <Rule Id="SA1118" Action="Error" /> <!-- ParameterMustNotSpanMultipleLines: A parameter to a C# method or indexer, other than the first parameter, spans across multiple lines. -->
    <Rule Id="SA1120" Action="Error" /> <!-- CommentsMustContainText: The C# comment does not contain any comment text. -->
    <Rule Id="SA1121" Action="Error" /> <!-- UseBuiltInTypeAlias: The code uses one of the basic C# types, but does not use the built-in alias for the type. -->
    <Rule Id="SA1122" Action="None" /> <!-- UseStringEmptyForEmptyStrings: The C# code includes an empty string, written as "". -->
    <Rule Id="SA1123" Action="Error" /> <!-- DoNotPlaceRegionsWithinElements: The C# code contains a region within the body of a code element. -->
    <Rule Id="SA1124" Action="Error" /> <!-- DoNotUseRegions: The C# code contains a region. -->
    <Rule Id="SA1125" Action="Error" /> <!-- UseShorthandForNullableTypes: The Nullable type has been defined not using the C# shorthand. -->
    <Rule Id="SA1126" Action="None" /> <!-- PrefixCallsCorrectly: A call to a member is not prefixed with the "this.", "base.", "object." or "typename." prefix to indicate the intended method call, within a C# code file. -->
    <Rule Id="SA1127" Action="Error" /> <!-- GenericTypeConstraintsMustBeOnOwnLine: A generic constraint on a type or method declaration is on the same line as the declaration, within a C# code file. -->
    <Rule Id="SA1128" Action="Error" /> <!-- ConstructorInitializerMustBeOnOwnLine: A constructor initializer is on the same line as the constructor declaration, within a C# code file. -->
    <Rule Id="SA1129" Action="Error" /> <!-- DoNotUseDefaultValueTypeConstructor: A value type was constructed using the syntax new T(). -->
    <Rule Id="SA1130" Action="Error" /> <!-- UseLambdaSyntax: An anonymous method was declared using the form delegate (parameters) { }, when a lambda expression would provide equivalent behavior with the syntax (parameters) => { }. -->
    <Rule Id="SA1131" Action="Error" /> <!-- UseReadableConditions: A comparison was made between a variable and a literal or constant value, and the variable appeared on the right-hand side of the expression. -->
    <Rule Id="SA1132" Action="Error" /> <!-- DoNotCombineFields: Two or more fields were declared in the same field declaration syntax. -->
    <Rule Id="SA1133" Action="Error" /> <!-- DoNotCombineAttributes: Two or more attributes appeared within the same set of square brackets. -->
    <Rule Id="SA1134" Action="Error" /> <!-- AttributesMustNotShareLine: An attribute is placed on the same line of code as another attribute or element. -->
    <Rule Id="SA1136" Action="Error" /> <!-- EnumValuesShouldBeOnSeparateLines: Multiple enum values are placed on the same line of code. -->
    <Rule Id="SA1137" Action="Error" /> <!-- ElementsShouldHaveTheSameIndentation: Two sibling elements which each start on their own line have different levels of indentation. -->
    <Rule Id="SA1139" Action="Error" /> <!-- UseLiteralsSuffixNotationInsteadOfCasting: Use literal suffix notation instead of casting. -->

    <!-- OrderingRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/OrderingRules.md -->
    <Rule Id="SA1200" Action="None" /> <!-- UsingDirectivesMustBePlacedCorrectly: A C# using directive is placed outside of a namespace element. -->
    <Rule Id="SA1201" Action="None" /> <!-- ElementsMustAppearInTheCorrectOrder: An element within a C# code file is out of order in relation to the other elements in the code. -->
    <Rule Id="SA1202" Action="Error" /> <!-- ElementsMustBeOrderedByAccess: An element within a C# code file is out of order within regard to access level, in relation to other elements in the code. -->
    <Rule Id="SA1203" Action="Error" /> <!-- ConstantsMustAppearBeforeFields: A constant field is placed beneath a non-constant field. -->
    <Rule Id="SA1204" Action="Error" /> <!-- StaticElementsMustAppearBeforeInstanceElements: A static element is positioned beneath an instance element of the same type. -->
    <Rule Id="SA1205" Action="Error" /> <!-- PartialElementsMustDeclareAccess: The partial element does not have an access modifier defined. -->
    <Rule Id="SA1206" Action="Error" /> <!-- DeclarationKeywordsMustFollowOrder: The keywords within the declaration of an element do not follow a standard ordering scheme. -->
    <Rule Id="SA1207" Action="Error" /> <!-- ProtectedMustComeBeforeInternal: The keyword protected is positioned after the keyword internal within the declaration of a protected internal C# element. -->
    <Rule Id="SA1208" Action="None" /> <!-- SystemUsingDirectivesMustBePlacedBeforeOtherUsingDirectives: A using directive which declares a member of the System namespace appears after a using directive which declares a member of a different namespace, within a C# code file. -->
    <Rule Id="SA1209" Action="Error" /> <!-- UsingAliasDirectivesMustBePlacedAfterOtherUsingDirectives: A using-alias directive is positioned before a regular using directive. -->
    <Rule Id="SA1210" Action="Error" /> <!-- UsingDirectivesMustBeOrderedAlphabeticallyByNamespace: The using directives within a C# code file are not sorted alphabetically by namespace. -->
    <Rule Id="SA1211" Action="Error" /> <!-- UsingAliasDirectivesMustBeOrderedAlphabeticallyByAliasName: The using-alias directives within a C# code file are not sorted alphabetically by alias name. -->
    <Rule Id="SA1212" Action="Error" /> <!-- PropertyAccessorsMustFollowOrder: A get accessor appears after a set accessor within a property or indexer. -->
    <Rule Id="SA1213" Action="Error" /> <!-- EventAccessorsMustFollowOrder: An add accessor appears after a remove accessor within an event. -->
    <Rule Id="SA1214" Action="Error" /> <!-- ReadonlyElementsMustAppearBeforeNonReadonlyElements: A readonly field is positioned beneath a non-readonly field. -->
    <Rule Id="SA1215" Action="Error" /> <!-- InstanceReadonlyElementsMustAppearBeforeInstanceNonReadonlyElements: An instance readonly element is positioned beneath an instance non-readonly element of the same type. -->
    <Rule Id="SA1216" Action="Error" /> <!-- UsingStaticDirectivesMustBePlacedAtTheCorrectLocation: A using static directive is positioned at the wrong location (before a regular using directive or after an alias using directive). -->
    <Rule Id="SA1217" Action="Error" /> <!-- UsingStaticDirectivesMustBeOrderedAlphabetically: The using static directives within a C# code file are not sorted alphabetically by full type name. -->

    <!-- NamingRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/NamingRules.md -->
    <Rule Id="SA1300" Action="Error" /> <!-- ElementMustBeginWithUpperCaseLetter: The name of a C# element does not begin with an upper-case letter. -->
    <Rule Id="SA1301" Action="None" /> <!-- ElementMustBeginWithLowerCaseLetter: There are currently no situations in which this rule will fire. -->
    <Rule Id="SA1302" Action="Error" /> <!-- InterfaceNamesMustBeginWithI: The name of a C# interface does not begin with the capital letter I. -->
    <Rule Id="SA1303" Action="Error" /> <!-- ConstFieldNamesMustBeginWithUpperCaseLetter: The name of a constant C# field should begin with an upper-case letter. -->
    <Rule Id="SA1304" Action="Error" /> <!-- NonPrivateReadonlyFieldsMustBeginWithUpperCaseLetter: The name of a non-private readonly C# field should being with an upper-case letter. -->
    <Rule Id="SA1305" Action="Error" /> <!-- FieldNamesMustNotUseHungarianNotation: The name of a field or variable in C# uses Hungarian notation. -->
    <Rule Id="SA1306" Action="Error" /> <!-- FieldNamesMustBeginWithLowerCaseLetter: The name of a field in C# does not begin with a lower-case letter. -->
    <Rule Id="SA1307" Action="Error" /> <!-- AccessibleFieldsMustBeginWithUpperCaseLetter: The name of a public or internal field in C# does not begin with an upper-case letter. -->
    <Rule Id="SA1308" Action="Error" /> <!-- VariableNamesMustNotBePrefixed: A field name in C# is prefixed with m_ or s_. -->
    <Rule Id="SA1309" Action="Error" /> <!-- FieldNamesMustNotBeginWithUnderscore: A field name in C# begins with an underscore. -->
    <Rule Id="SA1310" Action="Error" /> <!-- FieldNamesMustNotContainUnderscore: A field name in C# contains an underscore. -->
    <Rule Id="SA1311" Action="Error" /> <!-- StaticReadonlyFieldsMustBeginWithUpperCaseLetter: The name of a static readonly field does not begin with an upper-case letter. -->
    <Rule Id="SA1312" Action="Error" /> <!-- VariableNamesMustBeginWithLowerCaseLetter: The name of a variable in C# does not begin with a lower-case letter. -->
    <Rule Id="SA1313" Action="Error" /> <!-- ParameterNamesMustBeginWithLowerCaseLetter: The name of a parameter in C# does not begin with a lower-case letter. -->
    <Rule Id="SA1314" Action="Error" /> <!-- TypeParameterNamesMustBeginWithT: The name of a C# type parameter does not begin with the capital letter T -->

    <!-- MaintainabilityRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/MaintainabilityRules.md-->
    <Rule Id="SA1119" Action="Error" /> <!-- StatementMustNotUseUnnecessaryParenthesis: A C# statement contains parenthesis which are unnecessary and should be removed. -->
    <Rule Id="SA1400" Action="Error" /> <!-- AccessModifierMustBeDeclared: The access modifier for a C# element has not been explicitly defined. -->
    <Rule Id="SA1401" Action="Error" /> <!-- FieldsMustBePrivate: A field within a C# class has an access modifier other than private. -->
    <Rule Id="SA1402" Action="Error" /> <!-- FileMayOnlyContainASingleType: A C# code file contains more than one unique type. -->
    <Rule Id="SA1403" Action="Error" /> <!-- FileMayOnlyContainASingleNamespace: A C# code file contains more than one namespace. -->
    <Rule Id="SA1404" Action="Error" /> <!-- CodeAnalysisSuppressionMustHaveJustification: A Code Analysis SuppressMessage attribute does not include a justification. -->
    <Rule Id="SA1405" Action="Error" /> <!-- DebugAssertMustProvideMessageText: A call to Debug.Assert in C# code does not include a descriptive message. -->
    <Rule Id="SA1406" Action="Error" /> <!-- DebugFailMustProvideMessageText: A call to Debug.Fail in C# code does not include a descriptive message. -->
    <Rule Id="SA1407" Action="Error" /> <!-- ArithmeticExpressionsMustDeclarePrecedence: A C# statement contains a complex arithmetic expression which omits parenthesis around operators. -->
    <Rule Id="SA1408" Action="Error" /> <!-- ConditionalExpressionsMustDeclarePrecedence: A C# statement contains a complex conditional expression which omits parenthesis around operators. -->
    <Rule Id="SA1409" Action="Error" /> <!-- RemoveUnnecessaryCode: A C# file contains code which is unnecessary and can be removed without changing the overall logic of the code. -->
    <Rule Id="SA1410" Action="Error" /> <!-- RemoveDelegateParenthesisWhenPossible: A call to a C# anonymous method does not contain any method parameters, yet the statement still includes parenthesis. -->
    <Rule Id="SA1411" Action="Error" /> <!-- AttributeConstructorMustNotUseUnnecessaryParenthesis: An attribute declaration does not contain any parameters, yet it still includes parenthesis. -->
    <Rule Id="SA1413" Action="None" /> <!-- UseTrailingCommasInMultiLineInitializers: A multi-line initializer should use a comma on the last item. -->

    <!-- Layout Rules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/LayoutRules.md -->
    <Rule Id="SA1500" Action="Error" /> <!-- BracesForMultiLineStatementsMustNotShareLine: The opening or closing brace within a C# statement, element, or expression is not placed on its own line. -->
    <Rule Id="SA1501" Action="Error" /> <!-- StatementMustNotBeOnSingleLine: A C# statement containing opening and closing braces is written completely on a single line. -->
    <Rule Id="SA1502" Action="Error" /> <!-- ElementMustNotBeOnSingleLine: A C# element containing opening and closing braces is written completely on a single line. -->
    <Rule Id="SA1503" Action="Error" /> <!-- BracesMustNotBeOmitted: The opening and closing braces for a C# statement have been omitted. -->
    <Rule Id="SA1504" Action="Error" /> <!-- AllAccessorsMustBeSingleLineOrMultiLine: Within a C# property, indexer or event, at least one of the child accessors is written on a single line, and at least one of the child accessors is written across multiple lines. -->
    <Rule Id="SA1505" Action="Error" /> <!-- OpeningBracesMustNotBeFollowedByBlankLine: An opening brace within a C# element, statement, or expression is followed by a blank line. -->
    <Rule Id="SA1506" Action="Error" /> <!-- ElementDocumentationHeadersMustNotBeFollowedByBlankLine: An element documentation header above a C# element is followed by a blank line. -->
    <Rule Id="SA1507" Action="Error" /> <!-- CodeMustNotContainMultipleBlankLinesInARow: The C# code contains multiple blank lines in a row. -->
    <Rule Id="SA1508" Action="Error" /> <!-- ClosingBracesMustNotBePrecededByBlankLine: A closing brace within a C# element, statement, or expression is preceded by a blank line. -->
    <Rule Id="SA1509" Action="Error" /> <!-- OpeningBracesMustNotBePrecededByBlankLine: An opening brace within a C# element, statement, or expression is preceded by a blank line. -->
    <Rule Id="SA1510" Action="Error" /> <!-- ChainedStatementBlocksMustNotBePrecededByBlankLine: Chained C# statements are separated by a blank line. -->
    <Rule Id="SA1511" Action="Error" /> <!-- WhileDoFooterMustNotBePrecededByBlankLine: The while footer at the bottom of a do-while statement is separated from the statement by a blank line. -->
    <Rule Id="SA1512" Action="Error" /> <!-- SingleLineCommentsMustNotBeFollowedByBlankLine: A single-line comment within C# code is followed by a blank line. -->
    <Rule Id="SA1513" Action="Error" /> <!-- ClosingBraceMustBeFollowedByBlankLine: A closing brace within a C# element, statement, or expression is not followed by a blank line. -->
    <Rule Id="SA1514" Action="Error" /> <!-- ElementDocumentationHeaderMustBePrecededByBlankLine: An element documentation header above a C# element is not preceded by a blank line. -->
    <Rule Id="SA1515" Action="Error" /> <!-- SingleLineCommentMustBePrecededByBlankLine: A single-line comment within C# code is not preceded by a blank line. -->
    <Rule Id="SA1516" Action="Error" /> <!-- ElementsMustBeSeparatedByBlankLine: Adjacent C# elements are not separated by a blank line. -->
    <Rule Id="SA1517" Action="Error" /> <!-- CodeMustNotContainBlankLinesAtStartOfFile: The code file has blank lines at the start. -->
    <Rule Id="SA1518" Action="Error" /> <!-- UseLineEndingsCorrectlyAtEndOfFile: The line endings at the end of a file do not match the settings for the project. -->
    <Rule Id="SA1519" Action="Error" /> <!-- BracesMustNotBeOmittedFromMultiLineChildStatement: The opening and closing braces for a multi-line C# statement have been omitted. -->
    <Rule Id="SA1520" Action="Error" /> <!-- UseBracesConsistently: The opening and closing braces of a chained if/else if/else construct were included for some clauses, but omitted for others. -->

    <!-- DocumentationRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/DocumentationRules.md -->
    <Rule Id="SA1600" Action="None" /> <!-- ElementsMustBeDocumented: A C# code element is missing a documentation header. -->
    <Rule Id="SA1601" Action="None" /> <!-- PartialElementsMustBeDocumented: A C# partial element is missing a documentation header. -->
    <Rule Id="SA1602" Action="None" /> <!-- EnumerationItemsMustBeDocumented: An item within a C# enumeration is missing an Xml documentation header. -->
    <Rule Id="SA1603" Action="Error" /> <!-- DocumentationMustContainValidXml: The Xml within a C# element"s document header is badly formed. -->
    <Rule Id="SA1604" Action="Error" /> <!-- ElementDocumentationMustHaveSummary: The Xml header documentation for a C# element is missing a <summary> tag. -->
    <Rule Id="SA1605" Action="Error" /> <!-- PartialElementDocumentationMustHaveSummary: The <summary> or <content> tag within the documentation header for a C# code element is missing or empty. -->
    <Rule Id="SA1606" Action="Error" /> <!-- ElementDocumentationMustHaveSummaryText: The <summary> tag within the documentation header for a C# code element is empty. -->
    <Rule Id="SA1607" Action="Error" /> <!-- PartialElementDocumentationMustHaveSummaryText: The <summary> or <content> tag within the documentation header for a C# code element is empty. -->
    <Rule Id="SA1608" Action="Error" /> <!-- ElementDocumentationMustNotHaveDefaultSummary: The <summary> tag within an element"s Xml header documentation contains the default text generated by Visual Studio during the creation of the element. -->
    <Rule Id="SA1609" Action="Error" /> <!-- PropertyDocumentationMustHaveValue: The Xml header documentation for a C# property does not contain a <value> tag. -->
    <Rule Id="SA1610" Action="Error" /> <!-- PropertyDocumentationMustHaveValueText: The Xml header documentation for a C# property contains an empty <value> tag. -->
    <Rule Id="SA1611" Action="None" /> <!-- ElementParametersMustBeDocumented: A C# method, constructor, delegate or indexer element is missing documentation for one or more of its parameters. -->
    <Rule Id="SA1612" Action="Error" /> <!-- ElementParameterDocumentationMustMatchElementParameters: The documentation describing the parameters to a C# method, constructor, delegate or indexer element does not match the actual parameters on the element. -->
    <Rule Id="SA1613" Action="Error" /> <!-- ElementParameterDocumentationMustDeclareParameterName: A <param> tag within a C# element"s documentation header is missing a name attribute containing the name of the parameter. -->
    <Rule Id="SA1614" Action="Error" /> <!-- ElementParameterDocumentationMustHaveText: A <param> tag within a C# element"s documentation header is empty. -->
    <Rule Id="SA1615" Action="None" /> <!-- ElementReturnValueMustBeDocumented: A C# element is missing documentation for its return value. -->
    <Rule Id="SA1616" Action="Error" /> <!-- ElementReturnValueDocumentationMustHaveText: The <returns> tag within a C# element"s documentation header is empty. -->
    <Rule Id="SA1617" Action="Error" /> <!-- VoidReturnValueMustNotBeDocumented: A C# code element does not contain a return value, or returns void, but the documentation header for the element contains a <returns> tag. -->
    <Rule Id="SA1618" Action="None" /> <!-- GenericTypeParametersMustBeDocumented: A generic C# element is missing documentation for one or more of its generic type parameters. -->
    <Rule Id="SA1619" Action="None" /> <!-- GenericTypeParametersMustBeDocumentedPartialClass: A generic, partial C# element is missing documentation for one or more of its generic type parameters, and the documentation for the element contains a <summary> tag. -->
    <Rule Id="SA1620" Action="Error" /> <!-- GenericTypeParameterDocumentationMustMatchTypeParameters: The <typeparam> tags within the Xml header documentation for a generic C# element do not match the generic type parameters on the element. -->
    <Rule Id="SA1621" Action="Error" /> <!-- GenericTypeParameterDocumentationMustDeclareParameterName: A <typeparam> tag within the Xml header documentation for a generic C# element is missing a name attribute, or contains an empty name attribute. -->
    <Rule Id="SA1622" Action="Error" /> <!-- GenericTypeParameterDocumentationMustHaveText: A <typeparam> tag within the Xml header documentation for a generic C# element is empty. -->
    <Rule Id="SA1623" Action="Error" /> <!-- PropertySummaryDocumentationMustMatchAccessors: The documentation text within a C# property"s <summary> tag does not match the accessors within the property. -->
    <Rule Id="SA1624" Action="Error" /> <!-- PropertySummaryDocumentationMustOmitSetAccessorWithRestrictedAccess: The documentation text within a C# property"s <summary> tag takes into account all of the accessors within the property, but one of the accessors has limited access. -->
    <Rule Id="SA1625" Action="Error" /> <!-- ElementDocumentationMustNotBeCopiedAndPasted: The Xml documentation for a C# element contains two or more identical entries, indicating that the documentation has been copied and pasted. -->
    <Rule Id="SA1626" Action="Error" /> <!-- SingleLineCommentsMustNotUseDocumentationStyleSlashes: The C# code contains a single-line comment which begins with three forward slashes in a row. -->
    <Rule Id="SA1627" Action="Error" /> <!-- DocumentationTextMustNotBeEmpty: The Xml header documentation for a C# code element contains an empty tag. -->
    <Rule Id="SA1628" Action="Error" /> <!-- DocumentationTextMustBeginWithACapitalLetter: A section of the Xml header documentation for a C# element does not begin with a capital letter. -->
    <Rule Id="SA1630" Action="Error" /> <!-- DocumentationTextMustContainWhitespace: A section of the Xml header documentation for a C# element does not contain any whitespace between words. -->
    <Rule Id="SA1631" Action="Error" /> <!-- DocumentationMustMeetCharacterPercentage: A section of the Xml header documentation for a C# element does not contain enough alphabetic characters. -->
    <Rule Id="SA1632" Action="None" /> <!-- DocumentationTextMustMeetMinimumCharacterLength: From StyleCop 4.5 this rule is disabled by default. -->
    <Rule Id="SA1633" Action="None" /> <!-- FileMustHaveHeader: A C# code file is missing a standard file header. -->
    <Rule Id="SA1634" Action="None" /> <!-- FileHeaderMustShowCopyright: The file header at the top of a C# code file is missing a copyright tag. -->
    <Rule Id="SA1635" Action="None" /> <!-- FileHeaderMustHaveCopyrightText: The file header at the top of a C# code file is missing copyright text. -->
    <Rule Id="SA1636" Action="Error" /> <!-- FileHeaderCopyrightTextMustMatch: The file header at the top of a C# code file does not contain the appropriate copyright text. -->
    <Rule Id="SA1637" Action="Error" /> <!-- FileHeaderMustContainFileName: The file header at the top of a C# code file is missing the file name. -->
    <Rule Id="SA1638" Action="Error" /> <!-- FileHeaderFileNameDocumentationMustMatchFileName: The file tag within the file header at the top of a C# code file does not contain the name of the file. -->
    <Rule Id="SA1639" Action="Error" /> <!-- FileHeaderMustHaveSummary: The file header at the top of a C# code file does not contain a filled-in summary tag. -->
    <Rule Id="SA1640" Action="Error" /> <!-- FileHeaderMustHaveValidCompanyText: The file header at the top of a C# code file does not contain company name text. -->
    <Rule Id="SA1641" Action="Error" /> <!-- FileHeaderCompanyNameTextMustMatch: The file header at the top of a C# code file does not contain the appropriate company name text. -->
    <Rule Id="SA1642" Action="Error" /> <!-- ConstructorSummaryDocumentationMustBeginWithStandardText: The XML documentation header for a C# constructor does not contain the appropriate summary text. -->
    <Rule Id="SA1643" Action="Error" /> <!-- DestructorSummaryDocumentationMustBeginWithStandardText: The Xml documentation header for a C# finalizer does not contain the appropriate summary text. -->
    <Rule Id="SA1644" Action="Error" /> <!-- DocumentationHeadersMustNotContainBlankLines: A section within the Xml documentation header for a C# element contains blank lines. -->
    <Rule Id="SA1645" Action="Error" /> <!-- IncludedDocumentationFileDoesNotExist: An included Xml documentation file does not exist. -->
    <Rule Id="SA1646" Action="Error" /> <!-- IncludedDocumentationXPathDoesNotExist: An included Xml documentation link contains an invalid path. -->
    <Rule Id="SA1647" Action="Error" /> <!-- IncludeNodeDoesNotContainValidFileAndPath: An include tag within an Xml documentation header does not contain valid file and path attribute. -->
    <Rule Id="SA1648" Action="Error" /> <!-- InheritDocMustBeUsedWithInheritingClass: <inheritdoc> has been used on an element that doesn't inherit from a base class or implement an interface. -->
    <Rule Id="SA1649" Action="Error" /> <!-- FileNameMustMatchTypeName: The file name of a C# code file does not match the first type declared in the file. -->
    <Rule Id="SA1650" Action="Error" /> <!-- ElementDocumentationMustBeSpelledCorrectly: The element documentation for the element contains one or more spelling mistakes or unrecognized words. -->
    <Rule Id="SA1651" Action="Error" /> <!-- DoNotUsePlaceholderElements: The documentation for the element contains one or more <placeholder>elements. -->
    <Rule Id="SA1652" Action="None" /> <!-- EnableXmlDocumentationOutput: This rule was moved to SA0001 -->

    <!-- AlternativeRules: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/AlternativeRules.md -->
    <Rule Id="SX1101" Action="Error" /> <!-- DoNotPrefixLocalMembersWithThis: A call to an instance member of the local class or a base class is prefixed with "this.", within a C# code file. -->
    <Rule Id="SX1309" Action="None" /> <!-- FieldNamesMustBeginWithUnderscore: A field name does not begin with an underscore. -->
    <Rule Id="SX1309S" Action="None" /> <!-- StaticFieldNamesMustBeginWithUnderscore: A static field name does not begin with an underscore. -->

  </Rules>
</RuleSet>