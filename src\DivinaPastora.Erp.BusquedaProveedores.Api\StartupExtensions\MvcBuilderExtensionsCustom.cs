using Microsoft.Extensions.DependencyInjection;
using NLog;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class MvcBuilderExtensionsCustom
    {
        public static IMvcBuilder ConfigureCustomMvcBuilderBehaviors(this IMvcBuilder builder, Logger logger)
        {
            // TODO: A completar por el usuario...
            /* Ej.
            builder.ConfigureApiBehaviorOptions(opt =>
            {
                var builtInFactory = opt.InvalidModelStateResponseFactory;
                opt.InvalidModelStateResponseFactory = context =>
                {
                    var problemDetails = new
                    {
                        Type = "InvalidModelStateResponse",
                        context.HttpContext.Request.Path,
                        ProblemDetails = new ValidationProblemDetails(context.ModelState)
                    };

                    logger.Debug(problemDetails.ToJson());
                    return builtInFactory(context);
                };
            });
            */

            return builder;
        }
    }
}