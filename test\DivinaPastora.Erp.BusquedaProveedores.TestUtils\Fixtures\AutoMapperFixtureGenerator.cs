﻿using AutoMapper;
using AutoMapper.Configuration;
using DivinaPastora.Erp.BusquedaProveedores.Service;

namespace DivinaPastora.Erp.BusquedaProveedores.TestUtils.Fixtures
{
    public static class AutoMapperFixtureGenerator
    {
        public static IMapper GetAutoMapper()
        {
            var mapperConfig = new MapperConfigurationExpression();

            mapperConfig.AddProfile(typeof(ServicioProfile));
            return new MapperConfiguration(mapperConfig).CreateMapper();
        }
    }
}
