﻿using AutoMapper;
using DivinaPastora.Erp.BusquedaProveedores.Repository;
using DivinaPastora.Erp.BusquedaProveedores.Repository.Data;
using DivinaPastora.Erp.BusquedaProveedores.TestUtils.Fixtures;
using FluentAssertions;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Test
{
    public class BusquedaServiceTest
    {
        private Mock<IBusquedaRepository> repository;
        private IMapper mapper;
        private IBusquedaService sut;

        [Fact]
        public async Task GetCuadros()
        {
            SetupSut();
            var cuadros = BusquedaFixtureGenerator.GetCuadros();
            repository.Setup(x => x.GetCuadros()).Returns(cuadros);

            var result = await sut.GetCuadros();
            result.Should().BeEquivalentTo(await cuadros);
            repository.Verify(x => x.GetCuadros());
        }

        [Fact]
        public async Task GetCategorias()
        {
            var cuadroId = 1;
            SetupSut();
            var categorias = BusquedaFixtureGenerator.GetCategorias();
            repository.Setup(x => x.GetCategorias(cuadroId)).Returns(categorias);

            var result = await sut.GetCategorias(cuadroId);
            result.Should().BeEquivalentTo(await categorias);
            repository.Verify(x => x.GetCategorias(cuadroId));
        }

        [Fact]
        public async Task GetServicios()
        {
            var totalServicios = 2;
            var limit = 10;
            var offset = 0;
            var cuadroId = 1;
            var productoId = 105;
            var categoriaId = 1;
            SetupSut();
            var servicios = BusquedaFixtureGenerator.GetServicios();
            repository.Setup(x => x.SearchServicios(limit, offset, It.IsAny<BusquedaParams>())).Returns(servicios);
            repository.Setup(x => x.CountServicios(It.IsAny<BusquedaParams>())).Returns(totalServicios);

            var result = sut.Busqueda(cuadroId, productoId, categoriaId, limit, offset, null, null, null, null, null, null, null, null, null, null, null);

            result.GetAwaiter().GetResult().Servicios.Should().BeEquivalentTo(mapper.Map<List<Model.Servicio>>(await servicios));
            result.GetAwaiter().GetResult().TotalServicios.Should().Be(totalServicios);
            repository.Verify(x => x.SearchServicios(limit, offset, It.IsAny<BusquedaParams>()));
        }

        private void SetupSut()
        {
            repository = new Mock<IBusquedaRepository>();
            mapper = AutoMapperFixtureGenerator.GetAutoMapper();
            sut = new BusquedaService(mapper, repository.Object);
        }
    }
}
