using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using ProblemDetails = DivinaPastora.Libs.ProblemDetails.Model.ProblemDetails;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Factories
{
    public class ProblemDetailsFactoryCustom
    {
        private readonly ApiBehaviorOptions apiBehaviorOptions;

        public ProblemDetailsFactoryCustom(IOptions<ApiBehaviorOptions> apiBehaviorOptions)
        {
            this.apiBehaviorOptions = apiBehaviorOptions?.Value ?? throw new ArgumentNullException(nameof(apiBehaviorOptions));
        }

        public ProblemDetails CreateGenericProblemDetails()
        {
            var problemDetails = new ProblemDetails()
            {
                Status = 500
            };

            ApplyProblemDetailsDefaults(problemDetails);

            return problemDetails;
        }

        public ProblemDetails CreateProblemDetails(ProblemDetails problemDetails)
        {
            ApplyProblemDetailsDefaults(problemDetails);

            return problemDetails;
        }

        private void ApplyProblemDetailsDefaults(ProblemDetails problemDetails)
        {
            problemDetails.Status ??= 400;

            if (apiBehaviorOptions.ClientErrorMapping.TryGetValue(problemDetails.Status.Value, out var clientErrorData))
            {
                problemDetails.Title ??= clientErrorData.Title;
                problemDetails.Type ??= clientErrorData.Link;
            }
        }
    }
}