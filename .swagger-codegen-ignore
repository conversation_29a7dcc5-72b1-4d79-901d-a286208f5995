# Swagger Codegen Ignore
# Generated by swagger-codegen https://github.com/swagger-api/swagger-codegen

# Use this file to prevent files from being overwritten by the generator.
# The patterns follow closely to .gitignore or .dockerignore.

# As an example, the C# client generator defines ApiClient.cs.
# You can make changes and tell <PERSON><PERSON><PERSON> Codgen to ignore just this file by uncommenting the following line:
#ApiClient.cs

# You can match any string of characters against a directory, file or extension with a single asterisk (*):
#foo/*/qux
# The above matches foo/bar/qux and foo/baz/qux, but not foo/bar/baz/qux

# You can recursively match patterns against a directory, file or extension with a double asterisk (**):
#foo/**/qux
# This matches foo/bar/qux, foo/baz/qux, and foo/bar/baz/qux

# You can also negate patterns with an exclamation (!).
# For example, you can ignore all files in a docs folder with the file extension .md:
#docs/*.md
# Then explicitly reverse the ignore rule for a single file:
#!docs/README.md

.swagger-codegen-ignore
*.sln
README.md

**/*.csproj

test/**

src/*.Api/Properties/launchSettings.json
src/*.Api/Middlewares/AuthenticationMiddlewareCustom.cs
src/*.Api/Middlewares/ExceptionMiddlewareHandlers.cs
src/*.Api/Factories/ProblemDetailsFactoryCustom.cs
src/*.Api/StartupExtensions/AutoMapperExtensions.cs
src/*.Api/StartupExtensions/ClientFactoryExtensionsCustom.cs
src/*.Api/StartupExtensions/DependencyContainerExtensionsCustom.cs
src/*.Api/appsettings.*
src/*.Api/NLog.config

src/*.Service/Microservices/Audit/AuditClientCustom.cs

src/*.Service/Microservices/Authentication/AuthenticationServiceCustom.cs
src/*.Service/Microservices/Authentication/IAuthenticationServiceCustom.cs
src/*.Service/Microservices/Authentication/ScopeTokenClientCustom.cs

src/*.Model/Api/AppSettings/ServicesSettingsCustom.cs

src/*.Model/Authentication/AuthenticationDataCustom.cs
src/*.Model/Authentication/IAuthenticationDataCustom.cs

src/*.Service/*Service.cs

!src/*.Service/IServices.cs
!src/*.Service/Services.cs
!src/*.Service/ServicesMocked.cs
!src/*.Service/*Mocked.cs
# Esta línea habrá que comentarla si algún servicio va a comenzar por la letra I (ej. IItemService)
!src/*.Service/I*.cs