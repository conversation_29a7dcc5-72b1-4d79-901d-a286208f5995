using FluentAssertions;
using System.Reflection;
using Xunit;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Test
{
    /// <summary>
    /// Tests para verificar que la normalización de texto funciona correctamente
    /// para resolver problemas con caracteres especiales como Valencia vs València
    /// </summary>
    public class BusquedaRepositoryTextNormalizationTest
    {
        /// <summary>
        /// Test para verificar que el método NormalizeText funciona correctamente
        /// Este test usa reflexión para acceder al método privado estático
        /// </summary>
        [Theory]
        [InlineData("Valencia", "valencia")]
        [InlineData("València", "valencia")]
        [InlineData("Málaga", "malaga")]
        [InlineData("Malaga", "malaga")]
        [InlineData("Córdoba", "cordoba")]
        [InlineData("Cádiz", "cadiz")]
        [InlineData("Almería", "almeria")]
        [InlineData("<PERSON><PERSON><PERSON>", "jaen")]
        [InlineData("León", "leon")]
        [InlineData("Ávila", "avila")]
        [InlineData("", "")]
        [InlineData(null, null)]
        public void NormalizeText_DeberiaRemoverAcentosYConvertirAMinusculas(string input, string expected)
        {
            // Arrange - Usar reflexión para acceder al método privado estático
            var repositoryType = typeof(DivinaPastora.Erp.BusquedaProveedores.Repository.BusquedaRepository);
            var normalizeMethod = repositoryType.GetMethod("NormalizeText", BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var result = (string)normalizeMethod.Invoke(null, new object[] { input });

            // Assert
            result.Should().Be(expected);
        }

        /// <summary>
        /// Test adicional para verificar casos especiales de normalización
        /// </summary>
        [Fact]
        public void NormalizeText_DeberiaManejarcasosEspeciales()
        {
            // Arrange
            var repositoryType = typeof(DivinaPastora.Erp.BusquedaProveedores.Repository.BusquedaRepository);
            var normalizeMethod = repositoryType.GetMethod("NormalizeText", BindingFlags.NonPublic | BindingFlags.Static);

            // Test con texto mixto
            var mixedText = "Àlicante-Elche Àeropuerto";
            var result = (string)normalizeMethod.Invoke(null, new object[] { mixedText });
            result.Should().Be("alicante-elche aeropuerto");

            // Test con múltiples acentos
            var multiAccent = "Ñoño Ñuñez";
            result = (string)normalizeMethod.Invoke(null, new object[] { multiAccent });
            result.Should().Be("nono nunez");

            // Test con caracteres especiales que no son acentos
            var specialChars = "Test-123_ABC";
            result = (string)normalizeMethod.Invoke(null, new object[] { specialChars });
            result.Should().Be("test-123_abc");
        }
    }
}
