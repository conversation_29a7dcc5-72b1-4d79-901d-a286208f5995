﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Model.Authentication;
using IdentityModel;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares
{
    public partial class AuthenticationMiddleware
    {
        private readonly RequestDelegate next;

        public AuthenticationMiddleware(RequestDelegate next)
        {
            this.next = next;
        }

        public async Task InvokeAsync(HttpContext httpContext, IAuthenticationData authenticationData)
        {
            LoadAuthData(httpContext, authenticationData);
            await next(httpContext);
        }

        private void LoadAuthData(HttpContext httpContext, IAuthenticationData authenticationData)
        {
            authenticationData.Token = ParseToken(httpContext);
            authenticationData.UserName = ParseUserName(httpContext);
            authenticationData.ProcessId = ParseProcessId(httpContext);
            authenticationData.Roles = ParseRoles(httpContext);

            LoadAuthDataCustom(httpContext, authenticationData);
        }

        private string ParseUserName(HttpContext httpContext)
        {
            string userName = httpContext.User.Claims.SingleOrDefault(x => x.Type == "persona_id")?.Value;

            if (string.IsNullOrEmpty(userName))
            {
                return string.Empty;
            }

            return RemoveCompanySubstringFromInternalUsername(userName);
        }

        private string RemoveCompanySubstringFromInternalUsername(string userName)
        {
            if (userName.Contains("@"))
            {
                userName = userName.Substring(0, userName.IndexOf("@"));
            }

            return userName;
        }

        private string ParseProcessId(HttpContext httpContext)
        {
            return httpContext.User.Claims.SingleOrDefault(x => x.Type == "client_proceso_id")?.Value;
        }

        private List<string> ParseRoles(HttpContext httpContext)
        {
            return httpContext.User.Claims.Where(x => x.Type == JwtClaimTypes.Role || x.Type == ClaimTypes.Role).Select(x => x.Value).ToList();
        }

        private string ParseToken(HttpContext httpContext)
        {
            string token = httpContext.Request.Headers["Authorization"];

            if (string.IsNullOrEmpty(token))
            {
                return null;
            }

            return RemoveBearerStringFromToken(token);
        }

        private string RemoveBearerStringFromToken(string token)
        {
            return token.Contains(" ") ? token.Split(" ")[1] : token;
        }
    }
}
