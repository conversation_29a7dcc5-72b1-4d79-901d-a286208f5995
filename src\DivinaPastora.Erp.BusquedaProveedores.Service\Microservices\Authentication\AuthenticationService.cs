﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Model.Authentication;
using IdentityModel.Client;
using System.Net.Http;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Authentication
{
    public partial class AuthenticationService : IAuthenticationService
    {
        private readonly IScopeTokenClient scopeTokenClient;
        private readonly IAuthenticationData authenticationData;

        public AuthenticationService(IScopeTokenClient scopeTokenClient, IAuthenticationData authenticationData)
        {
            this.scopeTokenClient = scopeTokenClient;
            this.authenticationData = authenticationData;
        }

        public string GetTokenForScope(string scope)
        {
            return scopeTokenClient.GetToken(scope, authenticationData.Token);
        }

        public void SetTokenForScope(HttpClient httpClient, string scope)
        {
            httpClient.SetBearerToken(GetTokenForScope(scope));
        }
    }
}