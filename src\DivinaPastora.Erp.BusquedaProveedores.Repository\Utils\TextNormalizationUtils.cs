using System.Globalization;
using System.Text;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository.Utils
{
    /// <summary>
    /// Utilidades para normalización de texto en búsquedas
    /// </summary>
    public static class TextNormalizationUtils
    {
        /// <summary>
        /// Normaliza el texto removiendo acentos y diacríticos para búsquedas más flexibles.
        /// Convierte "València" a "valencia", "Málaga" a "malaga", etc.
        /// </summary>
        /// <param name="text">Texto a normalizar</param>
        /// <returns>Texto normalizado en minúsculas sin acentos</returns>
        public static string NormalizeText(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            // Normalizar a NFD (Normalization Form Decomposed) para separar caracteres base de diacríticos
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            // Filtrar solo los caracteres que no sean diacríticos (NonSpacingMark)
            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            // Convertir a minúsculas y normalizar de vuelta a NFC
            return stringBuilder.ToString().Normalize(NormalizationForm.FormC).ToLowerInvariant();
        }
    }
}
