﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using IdentityModel.Client;
using System.Collections.Generic;
using System.Net.Http;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Authentication
{
    public partial class ScopeTokenClient : IScopeTokenClient
    {
        private readonly HttpClient httpClient;

        public ScopeTokenClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public string GetToken(string scope, string token)
        {
            var discovery = httpClient.GetDiscoveryDocumentAsync().Result;
            var parameters = new Dictionary<string, string>();
            parameters.Add("token", token);
            parameters.Add("scope", scope);

            var response = httpClient.RequestTokenAsync(GetTokenRequest(discovery, parameters)).Result;

            return response.AccessToken;
        }
    }
}
