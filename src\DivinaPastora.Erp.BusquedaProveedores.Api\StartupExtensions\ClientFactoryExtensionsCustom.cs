﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class ClientFactoryExtensionsCustom
    {
        public static void AddClientFactoryCustom(this IServiceCollection services, IConfiguration configuration)
        {
            // TODO: A rellenar por el usuario...
        }
    }
}
