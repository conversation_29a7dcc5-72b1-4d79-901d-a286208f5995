using DivinaPastora.Erp.BusquedaProveedores.Repository;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SecureConnection.Models;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class DependencyContainerExtensionsCustom
    {
        public static IServiceCollection AddDependenciesCustom(this IServiceCollection services, IConfiguration config, IWebHostEnvironment env)
        {
            services.AddScoped<IBusquedaRepository, BusquedaRepository>();
            services.AddDbContext<BusquedaProveedoresContext>(options => options.UseSqlServer(GetConnectionString(config), x => x.UseNetTopologySuite()));
            return services;
        }

        public static string GetConnectionString(IConfiguration config)
        {
            string template = config.GetSection("Connection").GetValue<string>($"Template");
            if (template.Contains("%"))
            {
                SecureConnection.SecureConnection secureCon = new SecureConnection.SecureConnection();
                string filePath = config.GetSection("Connection").GetValue<string>("FilePath");
                string serialNumber = config.GetSection("Connection").GetValue<string>("SerialNumber");
                DataInfo connectionData = secureCon.ReadContentFile(filePath, serialNumber);
                return template.Replace("%user%", connectionData.User).Replace("%password%", connectionData.Password);
            }

            return template;
        }
    }
}
