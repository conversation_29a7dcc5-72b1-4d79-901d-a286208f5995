﻿using FluentAssertions;
using Microsoft.AspNetCore.Mvc;

namespace DivinaPastora.Erp.BusquedaProveedores.TestUtils.Asserts
{
    public static class IActionResultAssert
    {
        public static void AssertResult<TResult, TResponse>(this IActionResult result, TResponse expectedResponse)
            where TResult : ObjectResult
        {
            result.Should().BeOfType<TResult>();
            var response = result.GetResponse<TResult, TResponse>();
            response.Should().BeEquivalentTo(expectedResponse);
        }

        public static TValue GetResponse<TResult, TValue>(this IActionResult result)
            where TResult : ObjectResult
        {
            return (TValue)((TResult)result).Value;
        }
    }
}
