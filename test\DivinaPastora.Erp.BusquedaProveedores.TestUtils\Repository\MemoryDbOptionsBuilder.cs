﻿using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;

namespace DivinaPastora.Erp.BusquedaProveedores.TestUtils.Repository
{
    public static class MemoryDbOptionsBuilder
    {
        public static DbContextOptions<T> GetMemoryDatabaseOptions<T>([CallerMemberName] string caller = "")
            where T : DbContext
        {
            return new DbContextOptionsBuilder<T>()
                .UseInMemoryDatabase(databaseName: caller)
                .Options;
        }
    }
}
