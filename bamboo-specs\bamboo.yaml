version: 2
plan:
  project-key: ERP
  key: BP
  name: busqueda-proveedores
stages:
- Default Stage:
    manual: false
    final: false
    jobs:
    - Default Job
- Generate SDK:
    manual: false
    final: false
    jobs:
    - Generate SDK
- Deploy SDK:
    manual: false
    final: false
    jobs:
    - Deploy SDK 
- Tag y deploy master:
    manual: true
    final: false
    jobs:
    - Git tag 
Default Job:
  key: JOB1
  tasks:
  - checkout:
      repository: erp/busqueda-proveedores
      force-clean-build: 'false'
  - checkout:
      repository: build-tools/scripts
      path: ./build
      force-clean-build: 'false'
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\branch_type.ps1 ${bamboo.repository.branch.name}
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\jira_version.ps1 ${bamboo.jira.version} ${bamboo.repository.git.repositoryUrl}
  - inject-variables:
      file: ./branch_type.txt
      scope: RESULT
      namespace: inject
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\dotnet\restore.ps1
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\dotnet\build.ps1 ${bamboo.inject.branch_type}
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\dotnet\unit_test.ps1 ${bamboo.inject.branch_type} ${bamboo.buildNumber}
  - test-parser:
      type: mstest
      test-results: 
        - '**\TestResults\testresults_build_${bamboo.buildNumber}.trx'
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\dotnet\publish.ps1 ${bamboo.inject.branch_type}
      - ls
  - inject-variables:
      file: ./version.txt
      scope: RESULT
      namespace: inject
  artifacts:
  - name: Publish Folder
    location: out
    pattern: '**/*'
    shared: true
    required: false
  - name: Swagger Config
    location: src/DivinaPastora.Erp.BusquedaProveedores.Api/wwwroot
    pattern: '**/*.json'
    shared: true
    required: false
Generate SDK:
  key: GSDK
  tasks:
  - clean
  - checkout:
      repository: build-tools/scripts
      path: ./build
      force-clean-build: 'false'
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\create_client_SDK.ps1 ${bamboo.inject.branch_type}
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\build_client.ps1 ${bamboo.inject.branch_type}
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\create_nuget.ps1 ${bamboo.repository.branch.name} ${bamboo.inject.version} ${bamboo.inject.branch_type} ${bamboo.build.working.directory}
  artifacts:
  - name:  SDK
    location: package
    pattern: '**/*'
    shared: true
    required: false
Deploy SDK:
  key: DSDK
  tasks:
  - checkout:
      repository: build-tools/scripts
      path: ./build
      force-clean-build: 'false'
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build\base-scripts\deploy_defalla.ps1 ${bamboo.repository.branch.name}
Git tag:
  key: GT
  tasks:
  - checkout:
      repository: build-tools/scripts
      path: ./build-tools
      force-clean-build: 'false'
  - script:
      interpreter: WINDOWS_POWER_SHELL
      scripts:
      - .\build-tools\base-scripts\git_tag.ps1 ${bamboo.inject.branch_type} ${bamboo.inject.version} ${bamboo.planRepository.revision} ${bamboo.planRepository.repositoryUrl} ${bamboo.user} ${bamboo.pass}
branches:
  create: for-pull-request
  delete:
    after-deleted-days: 1
  link-to-jira: false
---
version: 2
deployment:
  name: ERP busqueda proveedores
  source-plan: ERP-BP
release-naming:
  next-version-name: ${bamboo.inject.version}
  applies-to-branches: true
  auto-increment: false
  auto-increment-variables: []
environments:
- Stage
- Production
- Production(brahms2)
- Production(brahms3)
- SDK
Stage:
  triggers:
  - stage-success:
      stage: Default Stage
      branch: develop
  tasks:
  - clean
  - artifact-download:
      artifacts:
      - {}
  - script:
      interpreter: SHELL
      scripts:
      - msdeploy.exe -verb:sync -source:contentPath="${bamboo.build.working.directory}" -dest:contentPath="Erp.BusquedaProveedores",ComputerName=srv-pruebas-web -enableRule:AppOffline
  final-tasks: []
  variables: {}
  requirements: []
  notifications: []
Production:
  triggers:
  - stage-success: Tag y deploy master
  tasks:
  - clean
  - artifact-download:
      artifacts:
      - {}
  - script:
      interpreter: SHELL
      scripts:
      - msdeploy.exe -verb:sync -source:contentPath="${bamboo.build.working.directory}" -dest:contentPath="Erp.BusquedaProveedores",ComputerName=brahms -enableRule:AppOffline
  final-tasks: []
  variables: {}
  requirements: []
  notifications: []
Production(brahms2):
  triggers:
  - stage-success: Tag y deploy master
  tasks:
  - clean
  - artifact-download:
      artifacts:
      - {}
  - script:
      interpreter: SHELL
      scripts:
      - msdeploy.exe -verb:sync -source:contentPath="${bamboo.build.working.directory}" -dest:contentPath="Erp.BusquedaProveedores",ComputerName=brahms2 -enableRule:AppOffline
  final-tasks: []
  variables: {}
  requirements: []
  notifications: []
Production(brahms3):
  triggers:
  - stage-success: Tag y deploy master
  tasks:
  - clean
  - artifact-download:
      artifacts:
      - {}
  - script:
      interpreter: SHELL
      scripts:
      - msdeploy.exe -verb:sync -source:contentPath="${bamboo.build.working.directory}" -dest:contentPath="Erp.BusquedaProveedores",ComputerName=brahms3 -enableRule:AppOffline
  final-tasks: []
  variables: {}
  requirements: []
  notifications: []
SDK:
  triggers:
  - stage-success: Tag y deploy master
  tasks:
  - clean
  - artifact-download:
      artifacts:
      - {}
  - script:
      interpreter: SHELL
      scripts:
      - C:\\nuget.exe push *.nupkg -Source http://defalla:1080 dpNuget1080
  final-tasks: []
  variables: {}
  requirements: []
  notifications: []
