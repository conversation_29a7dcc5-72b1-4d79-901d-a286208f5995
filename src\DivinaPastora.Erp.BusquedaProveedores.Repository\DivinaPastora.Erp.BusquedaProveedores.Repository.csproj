﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  
  <PropertyGroup>
    <CodeAnalysisRuleSet>..\..\StyleCopRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  
  <ItemGroup>
    <Compile Remove="test\**" />
    <EmbeddedResource Remove="test\**" />
    <None Remove="test\**" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="..\..\stylecop.json" Link="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="DivinaPastora.Erp.I18n" Version="0.2.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.36">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="6.0.36" />
    <PackageReference Include="NLog" Version="4.7.5" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Model\DivinaPastora.Erp.BusquedaProveedores.Model.csproj" />
  </ItemGroup>
</Project>