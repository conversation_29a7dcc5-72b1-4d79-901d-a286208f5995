﻿using DivinaPastora.Erp.BusquedaProveedores.Model.Audit;
using Microsoft.AspNetCore.Mvc.Filters;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Audit
{
    public partial class AuditClient : IAuditClient
    {
        private void FilterAudit(ResultExecutedContext context, string controllerName, string actionName, ref Request request, ref string response)
        {
            // TODO: En este método se puede incluir la lógica para reasignar el valor de campos que no quieran auditarse. Ejemplos:
            /*
            // Los campos tipo byte[] del body se guardarán como null en el log de Audit.
            if (request.Body != null)
            {
                var paramType = context.ActionDescriptor.Parameters[0].ParameterType;
                var body = JsonConvert.DeserializeObject(request.Body, paramType);

                foreach (PropertyInfo property in paramType.GetProperties())
                {
                    if (property.PropertyType.Name == "Byte[]")
                    {
                        property.SetValue(body, null);
                    }
                }

                request.Body = body.ToJson();
            }

            // Los campos tipo byte[] de la respuesta se guardarán como null en el log de Audit.
            OkObjectResult objResult = context.Result as OkObjectResult;
            if (objResult != null)
            {
                var modelResult = objResult.Value;
                var typeResult = modelResult.GetType();

                foreach (PropertyInfo property in typeResult.GetProperties())
                {
                    if (property.PropertyType.Name == "Byte[]")
                    {
                        property.SetValue(modelResult, null);
                    }
                }

                objResult.Value = modelResult;
                response = objResult.ToJson();
            }
            */
        }
    }
}