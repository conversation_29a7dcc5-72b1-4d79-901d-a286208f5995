/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class StaticFilesExtensions
    {
        public static void UseDefaultStaticFiles(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            DefaultFilesOptions options = new DefaultFilesOptions();
            if (env.IsProduction())
            {
                options.DefaultFileNames.Clear();
                options.DefaultFileNames.Add("index.production.html");
            }

            app.UseDefaultFiles(options);
            app.UseStaticFiles();
        }
    }
}
