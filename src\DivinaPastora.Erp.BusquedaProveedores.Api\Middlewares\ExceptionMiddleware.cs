﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Api.Factories;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Routing;
using System;
using System.Net;
using System.Threading.Tasks;
using ProblemDetails = DivinaPastora.Libs.ProblemDetails.Model.ProblemDetails;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares
{
    public partial class ExceptionMiddleware
    {
        private readonly RequestDelegate next;
        private readonly ProblemDetailsFactoryCustom problemDetailsFactory;
        private readonly IActionResultExecutor<ObjectResult> executor;

        public ExceptionMiddleware(RequestDelegate next, ProblemDetailsFactoryCustom problemDetailsFactory, IActionResultExecutor<ObjectResult> executor)
        {
            this.next = next;
            this.problemDetailsFactory = problemDetailsFactory;
            this.executor = executor;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await next(httpContext);
            }
            catch (Exception e)
            {
                await HandleExceptionAsync(httpContext, e);
            }
        }

        private Task WriteExceptionResponse(HttpContext context, HttpStatusCode httpStatusCode, ProblemDetails problemDetails)
        {
            RouteData routeData = context.GetRouteData() ?? new RouteData();

            context.Response.StatusCode = (int)httpStatusCode;

            ActionContext actionContext = new ActionContext(context, routeData, new ActionDescriptor());
            ObjectResult result = new ObjectResult(problemDetails);
            result.ContentTypes.Add("application/problem+json");
            result.ContentTypes.Add("application/problem+xml");

            return executor.ExecuteAsync(actionContext, result);
        }

        private bool IsLogErrorStatus(int? status)
        {
            return !status.HasValue || status == 0 || (status >= 401 && status <= 599);
        }
    }
}