﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Description>DivinaPastora.Erp.BusquedaProveedores.Api</Description>
    <Copyright>DivinaPastora.Erp.BusquedaProveedores.Api</Copyright>
    <TargetFramework>net6.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>DivinaPastora.Erp.BusquedaProveedores.Api</AssemblyName>
    <PackageId>DivinaPastora.Erp.BusquedaProveedores.Api</PackageId>
  </PropertyGroup>
  <PropertyGroup>
    <CodeAnalysisRuleSet>..\..\StyleCopRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\..\stylecop.json" Link="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="7.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.0.0" />
    <PackageReference Include="DivinaPastora.Libs.ProblemDetails" Version="0.0.1" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="6.0.36" />
    <PackageReference Include="SecureConnection" Version="1.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.36" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="5.6.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="5.6.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="5.6.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="5.6.3" />
    <PackageReference Include="NLog" Version="4.7.5" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.9.3" />
  </ItemGroup>
  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.1" />
  </ItemGroup>
  <ItemGroup>
      <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Model\DivinaPastora.Erp.BusquedaProveedores.Model.csproj" />
      <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Repository\DivinaPastora.Erp.BusquedaProveedores.Repository.csproj" />
      <ProjectReference Include="..\DivinaPastora.Erp.BusquedaProveedores.Service\DivinaPastora.Erp.BusquedaProveedores.Service.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="databaseCredentials.production.encrypted">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="databaseCredentials.staging.encrypted">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
