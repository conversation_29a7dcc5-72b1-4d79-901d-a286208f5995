/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Service
{
    public partial interface IBusquedaService
    {
        Task<ResultadoBusqueda> Busqueda(int cuadroId, int productoId, int categoriaId, int? limit, int? offset, string nombre, string provincia, string poblacion, string codigoPostal, string direccion, double? latitudSurOeste, double? longitudSurOeste, double? latitudNorEste, double? longitudNorEste, double? latitud, double? longitud);

        Task<List<Categoria>> GetCategorias(int cuadroId);

        Task<List<Cuadro>> GetCuadros();
    }
}
