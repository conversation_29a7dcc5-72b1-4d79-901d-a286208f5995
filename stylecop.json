// Configuration documentation: https://github.com/DotNetAnalyzers/StyleCopAnalyzers/blob/master/documentation/Configuration.md
{
  "$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json",
  "settings": {
    "documentationRules": {
      "companyName": "<PERSON><PERSON>",
      "copyrightText": "",
      "documentationCulture": "es-ES"
    },
    "orderingRules": {
      "elementOrder": [
        "kind",
        "constant",
        "static",
        "readonly",
        "accessibility"
      ],
      "usingDirectivesPlacement": "outsideNamespace",
      "blankLinesBetweenUsingGroups": "omit"
    },
    "indentation": {
      "indentationSize": 4,
      "tabSize": 4,
      "useTabs": false
    },
    "maintainabilityRules": {
      "topLevelTypes": [
        "class",
        "interface",
        "struct",
        "enum"
      ]
    }
  }
}