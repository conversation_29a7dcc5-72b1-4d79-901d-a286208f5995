using NetTopologySuite.Geometries;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository.Data
{
    public partial class Servicio
    {
        public Servicio(Servicio x)
        {
            Id = x.Id;
            CuadroId = x.CuadroId;
            ProductoId = x.ProductoId;
            CategoriaId = x.CategoriaId;
            Nombre = x.Nombre;
            Provincia = x.Provincia;
            Localidad = x.Localidad;
            CodigoPostal = x.CodigoPostal;
            Ruta = x.Ruta;
            Numero = x.Numero;
            DatosAdicionales = x.DatosAdicionales;
            Coordenadas = x.Coordenadas;
            Telefono = x.Telefono;
        }

        public Servicio()
        {
        }

        public int Id { get; set; }

        public int CuadroId { get; set; }

        public int ProductoId { get; set; }

        public int CategoriaId { get; set; }

        public string Nombre { get; set; }

        public string Provincia { get; set; }

        public string Localidad { get; set; }

        public string CodigoPostal { get; set; }

        public string Ruta { get; set; }

        public string Numero { get; set; }

        public string DatosAdicionales { get; set; }

        public Point Coordenadas { get; set; }

        public string Telefono { get; set; }

        public double? Distancia { get; set; }

        public int? Prioridad { get; set; }
    }
}
