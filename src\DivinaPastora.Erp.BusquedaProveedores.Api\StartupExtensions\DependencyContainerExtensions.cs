/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Api.Factories;
using DivinaPastora.Erp.BusquedaProveedores.Model.Authentication;
using DivinaPastora.Erp.BusquedaProveedores.Service;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class DependencyContainerExtensions
    {
        public static IServiceCollection AddDependencies(this IServiceCollection services, IWebHostEnvironment env)
        {
            services.AddServiceDependencies(env);

            return services;
        }

        private static void AddServiceDependencies(this IServiceCollection services, IWebHostEnvironment env)
        {
            if (env.EnvironmentName == "Fake")
            {
                services.AddScoped<IBusquedaService, BusquedaServiceMocked>();
            }
            else
            {
                services.AddScoped<IBusquedaService, BusquedaService>();
            }

            services.AddScoped<IAuthenticationData>(sd => new AuthenticationData(env.IsProduction()));

            services.AddSingleton<ProblemDetailsFactoryCustom>();
        }
    }
}
