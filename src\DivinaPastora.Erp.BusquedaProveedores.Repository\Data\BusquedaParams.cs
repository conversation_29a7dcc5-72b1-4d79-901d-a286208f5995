﻿using System;
using System.Collections.Generic;
using System.Text;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository.Data
{
    public class BusquedaParams
    {
        public int CuadroId { get; set; }

        public int ProductoId { get; set; }

        public int CategoriaId { get; set; }

        public string? Nombre { get; set; }

        public string? Provincia { get; set; }

        public string? Poblacion { get; set; }

        public string? CodigoPostal { get; set; }

        public string? Direccion { get; set; }

        public double? LatitudSurOeste { get; set; }

        public double? LongitudSurOeste { get; set; }

        public double? LatitudNorEste { get; set; }

        public double? LongitudNorEste { get; set; }

        public double? Longitud { get; set; }

        public double? Latitud { get; set; }
    }
}
