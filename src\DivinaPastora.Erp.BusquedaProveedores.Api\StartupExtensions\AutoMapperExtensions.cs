﻿using AutoMapper;
using DivinaPastora.Erp.BusquedaProveedores.Service;
using Microsoft.Extensions.DependencyInjection;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class AutoMapperExtensions
    {
        public static IServiceCollection AddAutoMapper(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(ServicioProfile));
            return services;
        }
    }
}