﻿<?xml version="1.0"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Warn"
      internalLogFile="c:\temp\internal-nlog.txt">

  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <targets>
    <target name="logFile" xsi:type="File"
            fileName="${basedir}\..\LogFiles\DivinaPastora.Erp.BusquedaProveedores\${shortdate}.log"
            encoding="utf-8"
            layout="[${date:universalTime=true:format=yyyy-MM-dd HH\:mm\:ss.fff}][${machinename}][${threadid}][${aspnet-TraceIdentifier}][${callsite}][${uppercase:${level}}] ${message} [Context: ${all-event-properties}] ${exception}" />
  </targets>
  <rules>
    <logger name="*" minlevel="${configsetting:name=NLog.MinLevel:default=Warn}" writeTo="logFile" />
  </rules>
</nlog>