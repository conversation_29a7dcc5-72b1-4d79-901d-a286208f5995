/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace DivinaPastora.Erp.BusquedaProveedores.Model
{
    public partial class Servicio
    {
        public int Id { get; set; }

        public int CuadroId { get; set; }

        public int ProductoId { get; set; }

        public int CategoriaId { get; set; }

        public string Nombre { get; set; }

        public string Provincia { get; set; }

        public string Localidad { get; set; }

        public string CodigoPostal { get; set; }

        public string Ruta { get; set; }

        public string Numero { get; set; }

        public string DatosAdicionales { get; set; }

        public string Telefono { get; set; }

        public double? Latitud { get; set; }

        public double? Longitud { get; set; }

        public double? Distancia { get; set; }

        public int Prioridad { get; set; }

        public Servicio()
        {
        }

        public Servicio(int id, int cuadroId, int productoId, int categoriaId, string nombre, string provincia, string localidad, string codigoPostal, string ruta, string numero, string datosAdicionales, string telefono, double? latitud, double? longitud, double? distancia, int prioridad)
        {
            Id = id;
            CuadroId = cuadroId;
            ProductoId = productoId;
            CategoriaId = categoriaId;
            Nombre = nombre;
            Provincia = provincia;
            Localidad = localidad;
            CodigoPostal = codigoPostal;
            Ruta = ruta;
            Numero = numero;
            DatosAdicionales = datosAdicionales;
            Telefono = telefono;
            Latitud = latitud;
            Longitud = longitud;
            Distancia = distancia;
            Prioridad = prioridad;
        }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            return obj.GetType() == GetType() && Equals((Servicio)obj);
        }

        public bool Equals(Servicio other)
        {
            if (other is null)
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }
#pragma warning disable SA1119 // Statement should not use unnecessary parenthesis
            return
                (Id == other.Id || (Id != null && Id.Equals(other.Id))) &&
                (CuadroId == other.CuadroId || (CuadroId != null && CuadroId.Equals(other.CuadroId))) &&
                (ProductoId == other.ProductoId || (ProductoId != null && ProductoId.Equals(other.ProductoId))) &&
                (CategoriaId == other.CategoriaId || (CategoriaId != null && CategoriaId.Equals(other.CategoriaId))) &&
                (Nombre == other.Nombre || (Nombre != null && Nombre.Equals(other.Nombre))) &&
                (Provincia == other.Provincia || (Provincia != null && Provincia.Equals(other.Provincia))) &&
                (Localidad == other.Localidad || (Localidad != null && Localidad.Equals(other.Localidad))) &&
                (CodigoPostal == other.CodigoPostal || (CodigoPostal != null && CodigoPostal.Equals(other.CodigoPostal))) &&
                (Ruta == other.Ruta || (Ruta != null && Ruta.Equals(other.Ruta))) &&
                (Numero == other.Numero || (Numero != null && Numero.Equals(other.Numero))) &&
                (DatosAdicionales == other.DatosAdicionales || (DatosAdicionales != null && DatosAdicionales.Equals(other.DatosAdicionales))) &&
                (Telefono == other.Telefono || (Telefono != null && Telefono.Equals(other.Telefono))) &&
                (Latitud == other.Latitud || (Latitud != null && Latitud.Equals(other.Latitud))) &&
                (Longitud == other.Longitud || (Longitud != null && Longitud.Equals(other.Longitud))) &&
                (Distancia == other.Distancia || (Distancia != null && Distancia.Equals(other.Distancia))) &&
                (Prioridad == other.Prioridad || (Prioridad != null && Prioridad.Equals(other.Prioridad)));
#pragma warning restore SA1119 // Statement should not use unnecessary parenthesis
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = 41;

                if (Id != null)
                {
                    hashCode = (hashCode * 59) + Id.GetHashCode();
                }

                if (CuadroId != null)
                {
                    hashCode = (hashCode * 59) + CuadroId.GetHashCode();
                }

                if (ProductoId != null)
                {
                    hashCode = (hashCode * 59) + ProductoId.GetHashCode();
                }

                if (CategoriaId != null)
                {
                    hashCode = (hashCode * 59) + CategoriaId.GetHashCode();
                }

                if (Nombre != null)
                {
                    hashCode = (hashCode * 59) + Nombre.GetHashCode();
                }

                if (Provincia != null)
                {
                    hashCode = (hashCode * 59) + Provincia.GetHashCode();
                }

                if (Localidad != null)
                {
                    hashCode = (hashCode * 59) + Localidad.GetHashCode();
                }

                if (CodigoPostal != null)
                {
                    hashCode = (hashCode * 59) + CodigoPostal.GetHashCode();
                }

                if (Ruta != null)
                {
                    hashCode = (hashCode * 59) + Ruta.GetHashCode();
                }

                if (Numero != null)
                {
                    hashCode = (hashCode * 59) + Numero.GetHashCode();
                }

                if (DatosAdicionales != null)
                {
                    hashCode = (hashCode * 59) + DatosAdicionales.GetHashCode();
                }

                if (Telefono != null)
                {
                    hashCode = (hashCode * 59) + Telefono.GetHashCode();
                }

                if (Latitud != null)
                {
                    hashCode = (hashCode * 59) + Latitud.GetHashCode();
                }

                if (Longitud != null)
                {
                    hashCode = (hashCode * 59) + Longitud.GetHashCode();
                }

                if (Distancia != null)
                {
                    hashCode = (hashCode * 59) + Distancia.GetHashCode();
                }

                if (Prioridad != null)
                {
                    hashCode = (hashCode * 59) + Prioridad.GetHashCode();
                }

                return hashCode;
            }
        }

        public static bool operator ==(Servicio left, Servicio right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(Servicio left, Servicio right)
        {
            return !Equals(left, right);
        }
    }
}
