﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System.Collections.Specialized;
using System.Web;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Audit
{
    public class QueryParamBuilder
    {
        private readonly NameValueCollection collection;
        private readonly string baseUri;

        public static QueryParamBuilder Create(string uriRequest)
        {
            return new QueryParamBuilder(uriRequest);
        }

        private QueryParamBuilder(string baseUri)
        {
            this.baseUri = baseUri;
            collection = HttpUtility.ParseQueryString("");
        }

        public QueryParamBuilder AddParam(string key, string value)
        {
            collection.Add(key, value);
            return this;
        }

        public override string ToString()
        {
            var queryParams = collection.ToString();
            if (string.IsNullOrEmpty(queryParams))
            {
                return baseUri;
            }

            return $"{baseUri}?{queryParams}";
        }
    }
}
