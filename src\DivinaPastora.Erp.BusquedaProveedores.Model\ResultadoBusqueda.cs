/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace DivinaPastora.Erp.BusquedaProveedores.Model
{
    public partial class ResultadoBusqueda
    {
        public int TotalServicios { get; set; }

        public List<Servicio> Servicios { get; set; }

        public ResultadoBusqueda()
        {
        }

        public ResultadoBusqueda(int totalServicios, List<Servicio> servicios)
        {
            TotalServicios = totalServicios;
            Servicios = servicios;
        }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            return obj.GetType() == GetType() && Equals((ResultadoBusqueda)obj);
        }

        public bool Equals(ResultadoBusqueda other)
        {
            if (other is null)
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }
#pragma warning disable SA1119 // Statement should not use unnecessary parenthesis
            return
                (TotalServicios == other.TotalServicios || (TotalServicios != null && TotalServicios.Equals(other.TotalServicios))) &&
                (Servicios == other.Servicios || (Servicios != null && other.Servicios != null && Servicios.SequenceEqual(other.Servicios)));
#pragma warning restore SA1119 // Statement should not use unnecessary parenthesis
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = 41;

                if (TotalServicios != null)
                {
                    hashCode = (hashCode * 59) + TotalServicios.GetHashCode();
                }

                if (Servicios != null)
                {
                    hashCode = (hashCode * 59) + Servicios.GetHashCode();
                }

                return hashCode;
            }
        }

        public static bool operator ==(ResultadoBusqueda left, ResultadoBusqueda right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(ResultadoBusqueda left, ResultadoBusqueda right)
        {
            return !Equals(left, right);
        }
    }
}
