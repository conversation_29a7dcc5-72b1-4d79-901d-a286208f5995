/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Filters.Swagger
{
    public class EnumDescriptorSchemaFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                var stringBuilder = new StringBuilder();

                BuildDescription(stringBuilder, context);
                BuildMemberDescription(stringBuilder, context);

                var description = stringBuilder.ToString();
                if (!string.IsNullOrEmpty(description))
                {
                    schema.Description = description;
                }
            }
        }

        private void BuildDescription(StringBuilder stringBuilder, SchemaFilterContext context)
        {
            if (context.Type.CustomAttributes.Any(x => x.AttributeType == typeof(DescriptionAttribute)))
            {
                DescriptionAttribute description = (DescriptionAttribute)context.Type.GetCustomAttributes(typeof(DescriptionAttribute), false).First();
                stringBuilder.AppendLine($"{description.Description}\n");
            }
        }

        private void BuildMemberDescription(StringBuilder stringBuilder, SchemaFilterContext context)
        {
            var enumValues = Enum.GetValues(context.Type);

            for (int i = 0; i < enumValues.Length; i++)
            {
                var enumValue = enumValues.GetValue(i);
                var enumIntegerValue = (int)enumValue;
                var member = context.Type.GetMember(enumValue.ToString()).Single();

                stringBuilder.AppendLine($"{enumIntegerValue} - {enumValue}\n");
            }
        }
    }
}