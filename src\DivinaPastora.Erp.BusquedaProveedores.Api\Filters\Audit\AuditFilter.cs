﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Audit;
using Microsoft.AspNetCore.Mvc.Filters;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Filters.Audit
{
    public class AuditFilter : ActionFilterAttribute
    {
        private readonly IAuditClient auditClient;

        public AuditFilter(IAuditClient auditClient)
        {
            this.auditClient = auditClient;
        }

        public override void OnResultExecuted(ResultExecutedContext context)
        {
            auditClient.AuditRequest(context);
            base.OnResultExecuted(context);
        }
    }
}
