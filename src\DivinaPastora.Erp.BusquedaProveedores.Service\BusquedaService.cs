using AutoMapper;
using DivinaPastora.Erp.BusquedaProveedores.Model;
using DivinaPastora.Erp.BusquedaProveedores.Repository;
using DivinaPastora.Erp.BusquedaProveedores.Service.Utils;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Data = DivinaPastora.Erp.BusquedaProveedores.Repository.Data;

namespace DivinaPastora.Erp.BusquedaProveedores.Service
{
    public class BusquedaService : IBusquedaService
    {
        private const int DefaultLimit = 15;
        private const int DefaultOffset = 0;

        private readonly IMapper mapper;
        private readonly IBusquedaRepository repository;

        public BusquedaService(IMapper mapper, IBusquedaRepository repository)
        {
            this.repository = repository;
            this.mapper = mapper;
        }

        public async Task<ResultadoBusqueda> Busqueda(int cuadroId, int productoId, int categoriaId, int? limit, int? offset, string nombre, string provincia, string poblacion, string codigoPostal, string direccion, double? latitudSurOeste, double? longitudSurOeste, double? latitudNorEste, double? longitudNorEste, double? latitud, double? longitud)
        {
            // Normalizar los parámetros de texto para búsquedas más flexibles
            var busquedaParams = new Data.BusquedaParams
            {
                CuadroId = cuadroId,
                ProductoId = productoId,
                CategoriaId = categoriaId,
                Nombre = TextNormalizationUtils.NormalizeText(nombre),
                Provincia = TextNormalizationUtils.NormalizeText(provincia),
                Poblacion = TextNormalizationUtils.NormalizeText(poblacion),
                CodigoPostal = codigoPostal,
                Direccion = TextNormalizationUtils.NormalizeText(direccion),
                Latitud = latitud,
                Longitud = longitud,
                LatitudSurOeste = latitudSurOeste,
                LongitudSurOeste = longitudSurOeste,
                LatitudNorEste = latitudNorEste,
                LongitudNorEste = longitudNorEste
            };

            AumentarViewport(busquedaParams);

            if (int.TryParse(codigoPostal, out var numeroCp))
            {
                busquedaParams.CodigoPostal = numeroCp.ToString("D5");
            }

            var result = new ResultadoBusqueda
            {
                Servicios = mapper.Map<List<Servicio>>(
                    (await repository.SearchServicios(
                        limit.GetValueOrDefault(DefaultLimit),
                        offset.GetValueOrDefault(DefaultOffset),
                        busquedaParams))

                    .ToList()),
                TotalServicios = repository.CountServicios(busquedaParams)
            };

            return result;
        }

        public async Task<List<Categoria>> GetCategorias(int cuadroId)
        {
            return mapper.Map<List<Categoria>>(await repository.GetCategorias(cuadroId));
        }

        public async Task<List<Cuadro>> GetCuadros()
        {
            return mapper.Map<List<Cuadro>>(await repository.GetCuadros());
        }

        private void AumentarViewport(Data.BusquedaParams busquedaParams)
        {
            if (TieneViewport(busquedaParams))
            {
                var factor = 1;
                var puntoMedioLatitud = (busquedaParams.LatitudNorEste + busquedaParams.LatitudSurOeste) / 2;
                var puntoMedioLongitud = (busquedaParams.LongitudNorEste + busquedaParams.LongitudSurOeste) / 2;

                var latitudNorEsteEscalada = busquedaParams.LatitudNorEste + (factor * (busquedaParams.LatitudNorEste - puntoMedioLatitud));
                var longitudNorEsteEscalada = busquedaParams.LongitudNorEste + (factor * (busquedaParams.LongitudNorEste - puntoMedioLongitud));
                var latitudSurOesteEscalada = busquedaParams.LatitudSurOeste + (factor * (busquedaParams.LatitudSurOeste - puntoMedioLatitud));
                var longitudSurOesteEscalada = busquedaParams.LongitudSurOeste + (factor * (busquedaParams.LongitudSurOeste - puntoMedioLongitud));

                busquedaParams.LatitudNorEste = latitudNorEsteEscalada;
                busquedaParams.LongitudNorEste = longitudNorEsteEscalada;
                busquedaParams.LatitudSurOeste = latitudSurOesteEscalada;
                busquedaParams.LongitudSurOeste = longitudSurOesteEscalada;
            }
        }

        private bool TieneViewport(Data.BusquedaParams busquedaParams)
        {
            return busquedaParams.LatitudNorEste.HasValue && busquedaParams.LatitudSurOeste.HasValue && busquedaParams.LongitudNorEste.HasValue && busquedaParams.LongitudSurOeste.HasValue;
        }
    }
}
