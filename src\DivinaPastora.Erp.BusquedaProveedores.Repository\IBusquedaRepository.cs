﻿using DivinaPastora.Erp.BusquedaProveedores.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository
{
    public interface IBusquedaRepository
    {
        Task<List<Cuadro>> GetCuadros();

        Task<List<Categoria>> GetCategorias(int cuadroId);

        Task<List<Data.Servicio>> SearchServicios(int limit, int offset, Data.BusquedaParams busquedaParams);

        int CountServicios(Data.BusquedaParams busquedaParams);
    }
}
