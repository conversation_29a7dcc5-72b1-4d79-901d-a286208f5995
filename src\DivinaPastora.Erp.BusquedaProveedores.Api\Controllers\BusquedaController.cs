/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Api.Binders;
using DivinaPastora.Erp.BusquedaProveedores.Model;
using DivinaPastora.Erp.BusquedaProveedores.Service;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using ProblemDetails = DivinaPastora.Libs.ProblemDetails.Model.ProblemDetails;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Controllers
{
    [ApiController]
    public class BusquedaController : Controller
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        private readonly IBusquedaService busquedaService;

        public BusquedaController(IBusquedaService service)
        {
            busquedaService = service;
        }

        /// <summary>
        /// Obtiene una lista de servicios/proveedores en función de los parámetros de búsqueda
        /// </summary>
        [HttpGet]
        [Route("/busqueda")]
        [Produces("application/json")]
        [SwaggerOperation(OperationId = "Busqueda", Description = "Obtiene una lista de servicios/proveedores en función de los parámetros de búsqueda")]
        [SwaggerResponse(statusCode: 200, type: typeof(ResultadoBusqueda), description: "OK")]
        [SwaggerResponse(statusCode: 400, type: typeof(ProblemDetails), description: "Bad Request")]
        [SwaggerResponse(statusCode: 500, type: typeof(ProblemDetails), description: "Internal Server Error")]
        public async virtual Task<IActionResult> Busqueda([FromQuery][SwaggerParameter("Id del cuadro en el que se realiza la búsqueda")][Required]int cuadroId, [FromQuery][SwaggerParameter("Id del producto sobre el que se realiza la búsqueda")][Required]int productoId, [FromQuery][SwaggerParameter("Id de la categoria sobre el que se realiza la búsqueda")][Required]int categoriaId, [FromQuery][SwaggerParameter("Máximo número de elementos de la respuesta")]int? limit, [FromQuery][SwaggerParameter("Número de elementos que no se incluirán en la respuesta (número de página)")]int? offset, [FromQuery][SwaggerParameter("Nombre del producto/servicio a buscar")]string nombre, [FromQuery][SwaggerParameter("Provincia del producto/servicio a buscar")]string provincia, [FromQuery][SwaggerParameter("Población del producto/servicio a buscar")]string poblacion, [FromQuery][SwaggerParameter("Código Postal del producto/servicio a buscar")]string codigoPostal, [FromQuery][SwaggerParameter("Dirección del producto/servicio a buscar")]string direccion, [FromQuery][SwaggerParameter("Latitud de la esquina inferior izquierda de la ventana de búsqueda de productos/servicios")]double? latitudSurOeste, [FromQuery][SwaggerParameter("Longitud de la esquina inferior izquierda de la ventana de búsqueda de productos/servicios")]double? longitudSurOeste, [FromQuery][SwaggerParameter("Latitud de la esquina superior derecha de la ventana de búsqueda de productos/servicios")]double? latitudNorEste, [FromQuery][SwaggerParameter("Longitud de la esquina superior derecha de la ventana de búsqueda de productos/servicios")]double? longitudNorEste, [FromQuery][SwaggerParameter("Latitud de la consulta, para ordenar los resultados por distancia")]double? latitud, [FromQuery][SwaggerParameter("Longitud de la consulta, para ordenar los resultados por distancia")]double? longitud)
        {
            Logger.Debug($"Start Busqueda. Parameters: cuadroId={JsonConvert.SerializeObject(cuadroId)}, productoId={JsonConvert.SerializeObject(productoId)}, categoriaId={JsonConvert.SerializeObject(categoriaId)}, limit={JsonConvert.SerializeObject(limit)}, offset={JsonConvert.SerializeObject(offset)}, nombre={JsonConvert.SerializeObject(nombre)}, provincia={JsonConvert.SerializeObject(provincia)}, poblacion={JsonConvert.SerializeObject(poblacion)}, codigoPostal={JsonConvert.SerializeObject(codigoPostal)}, direccion={JsonConvert.SerializeObject(direccion)}, latitudSurOeste={JsonConvert.SerializeObject(latitudSurOeste)}, longitudSurOeste={JsonConvert.SerializeObject(longitudSurOeste)}, latitudNorEste={JsonConvert.SerializeObject(latitudNorEste)}, longitudNorEste={JsonConvert.SerializeObject(longitudNorEste)}, latitud={JsonConvert.SerializeObject(latitud)}, longitud={JsonConvert.SerializeObject(longitud)}");

            var result = await busquedaService.Busqueda(cuadroId, productoId, categoriaId, limit, offset, nombre, provincia, poblacion, codigoPostal, direccion, latitudSurOeste, longitudSurOeste, latitudNorEste, longitudNorEste, latitud, longitud);
            return StatusCode(200, result);
        }

        /// <summary>
        /// Obtiene una lista de categorías de servicios/proveedores
        /// </summary>
        [HttpGet]
        [Route("/categorias/{cuadroId}")]
        [Produces("application/json")]
        [SwaggerOperation(OperationId = "GetCategorias", Description = "Obtiene una lista de categorías de servicios/proveedores")]
        [SwaggerResponse(statusCode: 200, type: typeof(List<Categoria>), description: "OK")]
        [SwaggerResponse(statusCode: 400, type: typeof(ProblemDetails), description: "Bad Request")]
        [SwaggerResponse(statusCode: 500, type: typeof(ProblemDetails), description: "Internal Server Error")]
        public async virtual Task<IActionResult> GetCategorias([FromRoute][SwaggerParameter("ID del cuadro al que pertenecen las categorías")][Required]int cuadroId)
        {
            Logger.Debug($"Start GetCategorias. Parameters: cuadroId={JsonConvert.SerializeObject(cuadroId)}");

            var result = await busquedaService.GetCategorias(cuadroId);
            return StatusCode(200, result);
        }

        /// <summary>
        /// Obtiene una lista de cuadros de servicios/proveedores
        /// </summary>
        [HttpGet]
        [Route("/cuadros")]
        [Produces("application/json")]
        [SwaggerOperation(OperationId = "GetCuadros", Description = "Obtiene una lista de cuadros de servicios/proveedores")]
        [SwaggerResponse(statusCode: 200, type: typeof(List<Cuadro>), description: "OK")]
        [SwaggerResponse(statusCode: 400, type: typeof(ProblemDetails), description: "Bad Request")]
        [SwaggerResponse(statusCode: 500, type: typeof(ProblemDetails), description: "Internal Server Error")]
        public async virtual Task<IActionResult> GetCuadros()
        {
            Logger.Debug($"Start GetCuadros. Parameters: ");

            var result = await busquedaService.GetCuadros();
            return StatusCode(200, result);
        }
    }
}
