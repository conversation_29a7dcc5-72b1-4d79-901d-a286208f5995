﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using Newtonsoft.Json;
using System;
using System.Globalization;

namespace DivinaPastora.Erp.BusquedaProveedores.Model.Extensions
{
    public static class ObjectExtension
    {
        public static string ToJson(this object item)
        {
            var serializerSettings = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, ReferenceLoopHandling = ReferenceLoopHandling.Ignore };
            return JsonConvert.SerializeObject(item, Formatting.None, serializerSettings);
        }

        public static T FromJson<T>(this string serializedItem)
        {
            return JsonConvert.DeserializeObject<T>(serializedItem);
        }

        public static object FromJson(this string serializedItem, Type type)
        {
            return JsonConvert.DeserializeObject(serializedItem, type);
        }

        public static object ToType<T>(this object value)
        {
            return value.ToType(typeof(T));
        }

        public static object ToType(this object value, Type type)
        {
            if (value.GetType() == type)
            {
                return value;
            }

            var culture = (CultureInfo)CultureInfo.CurrentCulture.Clone();
            culture.NumberFormat.NumberDecimalSeparator = ".";
            culture.NumberFormat.NumberGroupSeparator = ",";

            return Convert.ChangeType(value, type, culture);
        }
    }
}
