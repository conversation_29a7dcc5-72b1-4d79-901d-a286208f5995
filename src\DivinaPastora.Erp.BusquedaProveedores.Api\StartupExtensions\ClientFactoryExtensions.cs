﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Model.Api.AppSettings;
using DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Net.Http;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class ClientFactoryExtensions
    {
        public static void AddClientFactory(this IServiceCollection services, IConfiguration configuration)
        {
        }

        public static void AddMicroserviceClient<TIClient, TClient>(this IServiceCollection services, string uri)
            where TIClient : class
            where TClient : class, TIClient
        {
            services.AddHttpClient<TIClient, TClient>(client =>
            {
                client.BaseAddress = new Uri(uri);
                client.SetDefaultRequestHeaders();
            });
        }

        public static void SetDefaultRequestHeaders(this HttpClient client)
        {
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("User-Agent", "BusquedaProveedores");
        }
    }
}