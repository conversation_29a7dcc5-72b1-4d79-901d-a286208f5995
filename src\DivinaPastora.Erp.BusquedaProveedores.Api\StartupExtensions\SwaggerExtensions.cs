/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Api.Filters.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class SwaggerExtensions
    {
        public static IServiceCollection AddSwagger(this IServiceCollection services, IConfiguration config, IWebHostEnvironment env)
        {
            services.AddSwaggerGen(c =>
            {
                var swaggerDocInfo = new OpenApiInfo
                {
                    Version = "0.1.0",
                    Title = "Busqueda Proveedores",
                    Description = "Busqueda Proveedores",
                };
                c.SwaggerDoc("0.1.0", swaggerDocInfo);
                c.EnableAnnotations();
                c.<PERSON>ilt<PERSON><EnumDescriptorSchemaFilter>();
                c.<PERSON><ParamFromQueryModelFilter>();
            });

            return services;
        }

        public static void UseSwagger(this IApplicationBuilder app, IConfiguration config, IWebHostEnvironment env)
        {
            app.UseSwagger();

            app.UseReDoc(c =>
            {
                c.SpecUrl("/swagger/0.1.0/swagger.json");
            });

            if (env.EnvironmentName != "Production")
            {
                app.UseSwaggerUI(c =>
                {
                    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);

                    // TODO: Either use the SwaggerGen generated Swagger contract (generated from C# classes)
                    c.SwaggerEndpoint("/swagger/0.1.0/swagger.json", "Busqueda Proveedores");

                    // TODO: Or alternatively use the original Swagger contract that's included in the static files
                    //// c.SwaggerEndpoint("/swagger-original.json", "Busqueda Proveedores Original");
                });
            }
        }
    }
}
