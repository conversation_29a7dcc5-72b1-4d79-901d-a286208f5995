<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <CodeAnalysisRuleSet>..\..\StyleCopRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\..\stylecop.json" Link="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" />
    <PackageReference Include="FluentAssertions" Version="5.10.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.36" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\DivinaPastora.Erp.BusquedaProveedores.Api\DivinaPastora.Erp.BusquedaProveedores.Api.csproj" />
    <ProjectReference Include="..\..\src\DivinaPastora.Erp.BusquedaProveedores.Model\DivinaPastora.Erp.BusquedaProveedores.Model.csproj" />
  </ItemGroup>
</Project>
