﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Model.Audit;
using DivinaPastora.Erp.BusquedaProveedores.Model.Authentication;
using DivinaPastora.Erp.BusquedaProveedores.Model.Extensions;
using Microsoft.AspNetCore.Mvc.Filters;
using NLog;
using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Audit
{
    public partial class AuditClient : IAuditClient
    {
        private const string InsertAuditUri = "audit";

        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly HttpClient httpClient;
        private readonly IAuthenticationData authenticationData;

        public AuditClient(HttpClient httpClient, IAuthenticationData authenticationData)
        {
            this.httpClient = httpClient;
            this.authenticationData = authenticationData;
        }

        public Task<bool> AuditRequest(ResultExecutedContext context)
        {
            string user = authenticationData.UserName;
            string controllerName = context.Controller.ToString();
            string actionName = (string)context.RouteData.Values["action"];
            string result = context.Result.ToJson();

            var request = context.HttpContext.Request;
            var route = request.Path.HasValue ? request.Path.Value : null;
            var queryParams = request.QueryString.HasValue ? request.QueryString.Value : null;
            var requestHeader = request.Headers.Aggregate("", (current, header) => current + $"{header.Key}: {header.Value}{Environment.NewLine}");

            string requestBody = null;
            if (request.Body.Length > 0)
            {
                using var stream = new StreamReader(request.Body);
                stream.BaseStream.Position = 0;
                requestBody = stream.ReadToEnd();
            }

            var inputParams = new Request(route, queryParams, requestHeader, requestBody);

            FilterAudit(context, controllerName, actionName, ref inputParams, ref result);

            return InsertAudit(user, controllerName, actionName, inputParams, result);
        }

        public async Task<bool> InsertAudit(string user, string controllerName, string actionName, Request jsonInputParams, string response)
        {
            try
            {
                var uriRequest = QueryParamBuilder.Create(InsertAuditUri).ToString();

                var content = new
                {
                    User = user,
                    ControllerName = controllerName,
                    ApiCall = actionName,
                    Params = jsonInputParams?.ToJson(),
                    Response = response
                };

                HttpContent httpContent = new StringContent(content.ToJson(), Encoding.UTF8, "application/json");
                var responseClient = await httpClient.PostAsync(uriRequest, httpContent);
                var result = await responseClient.Content.ReadAsStringAsync();
                return bool.Parse(result);
            }
            catch (Exception e)
            {
                Logger.Error(e);
                return false;
            }
        }
    }
}