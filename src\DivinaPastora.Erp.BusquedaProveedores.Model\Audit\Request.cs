﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

namespace DivinaPastora.Erp.BusquedaProveedores.Model.Audit
{
    public class Request
    {
        public string Route { get; set; }

        public string QueryParams { get; set; }

        public string Header { get; set; }

        public string Body { get; set; }

        public Request(string route, string queryParams, string header, string body)
        {
            Route = route;
            QueryParams = queryParams;
            Header = header;
            Body = body;
        }
    }
}
