﻿using DivinaPastora.Libs.ProblemDetails.Exception;
using Microsoft.AspNetCore.Http;
using System;
using System.Net;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares
{
    public partial class ExceptionMiddleware
    {
        private Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            return exception switch
            {
                ProblemDetailsException problemDetailsException => HandleProblemDetailsException(context, problemDetailsException),
                _ => HandleGenericException(context)
            };
        }

        private Task HandleGenericException(HttpContext context)
        {
            return WriteExceptionResponse(
                context,
                HttpStatusCode.InternalServerError,
                problemDetailsFactory.CreateGenericProblemDetails());
        }

        private Task HandleProblemDetailsException(HttpContext context, ProblemDetailsException problemDetailsException)
        {
            return WriteExceptionResponse(
                context,
                problemDetailsException.ProblemDetails.Status != null ? (HttpStatusCode)problemDetailsException.ProblemDetails.Status : HttpStatusCode.BadRequest,
                problemDetailsFactory.CreateProblemDetails(problemDetailsException.ProblemDetails));
        }
    }
}