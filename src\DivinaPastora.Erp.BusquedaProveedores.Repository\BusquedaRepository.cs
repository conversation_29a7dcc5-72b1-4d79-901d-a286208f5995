using DivinaPastora.Erp.BusquedaProveedores.Model;
using DivinaPastora.Erp.BusquedaProveedores.Repository.Data;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository
{
    public class BusquedaRepository : IBusquedaRepository
    {
        private const int CoordinateSystem = 4326;
        private readonly BusquedaProveedoresContext context;

        public BusquedaRepository(BusquedaProveedoresContext busquedaProveedoresContext)
        {
            context = busquedaProveedoresContext;
        }

        public async Task<List<Cuadro>> GetCuadros()
        {
            return await context.Cuadros.ToListAsync();
        }

        public async Task<List<Categoria>> GetCategorias(int cuadroId)
        {
            return await context.Categorias
                .Where(x => x.CuadroId == cuadroId).ToListAsync();
        }

        public async Task<List<Data.Servicio>> SearchServicios(int limit, int offset, Data.BusquedaParams busquedaParams)
        {
            var servicios = BuildQuery(busquedaParams);

            if (busquedaParams.Latitud != null && busquedaParams.Longitud != null)
            {
                var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory(CoordinateSystem);
                var currentLocation = geometryFactory.CreatePoint(new Coordinate(busquedaParams.Longitud.Value, busquedaParams.Latitud.Value));

                servicios = servicios.OrderByDescending(x => x.Prioridad)
                    .ThenBy(x => x.Coordenadas.Distance(currentLocation));
                servicios = servicios.Select(x => new Data.Servicio(x)
                {
                    Distancia = x.Coordenadas.Distance(currentLocation)
                });
            }
            else
            {
                servicios = servicios.OrderByDescending(x => x.Prioridad).ThenBy(x => x.Nombre);
            }

            servicios = servicios.Skip(offset);
            servicios = servicios.Take(limit);

            return await Task.Run(() => servicios.ToList());
        }

        public int CountServicios(BusquedaParams busquedaParams)
        {
            var servicios = GetServicios(busquedaParams);
            return servicios.Count();
        }

        private IQueryable<Data.Servicio> BuildQuery(BusquedaParams busquedaParams)
        {
            var servicios = GetServicios(busquedaParams);

            servicios = servicios.Where(x => x.CuadroId == busquedaParams.CuadroId);
            servicios = servicios.Where(x => x.ProductoId == busquedaParams.ProductoId);
            servicios = servicios.Where(x => x.CategoriaId == busquedaParams.CategoriaId);

            if (!string.IsNullOrEmpty(busquedaParams.Nombre))
            {
                servicios = servicios.Where(x => EF.Functions.Collate(x.Nombre, "Modern_Spanish_CI_AI").Contains(busquedaParams.Nombre));
            }

            if (string.IsNullOrEmpty(busquedaParams.CodigoPostal))
            {
                if (!string.IsNullOrEmpty(busquedaParams.Provincia))
                {
                    servicios = servicios.Where(x => EF.Functions.Collate(x.Provincia, "Modern_Spanish_CI_AI").Contains(busquedaParams.Provincia));
                }

                if (!string.IsNullOrEmpty(busquedaParams.Poblacion))
                {
                    servicios = servicios.Where(x => EF.Functions.Collate(x.Localidad, "Modern_Spanish_CI_AI").Contains(busquedaParams.Poblacion));
                }
            }

            if (!string.IsNullOrEmpty(busquedaParams.Direccion))
            {
                servicios = servicios.Where(x => EF.Functions.Collate(x.Ruta, "Modern_Spanish_CI_AI").Contains(busquedaParams.Direccion));
            }

            return servicios;
        }

        private IQueryable<Data.Servicio> GetServicios(BusquedaParams busquedaParams)
        {
            var servicioCodigoPostal = BuildQueryCodigoPostal(busquedaParams);
            var servicioCoordenadas = BuildQueryCoordenadas(busquedaParams);
            var servicioLocalidad = BuildQueryLocalidad(busquedaParams);

            var servicios = servicioCodigoPostal
                .Union(servicioCoordenadas)
                .Union(servicioLocalidad);

            return servicios.AsQueryable();
        }

        private List<Data.Servicio> BuildQueryCoordenadas(BusquedaParams busquedaParams)
        {
            if (busquedaParams.LatitudNorEste != null && busquedaParams.LongitudNorEste != null && busquedaParams.LatitudSurOeste != null && busquedaParams.LongitudSurOeste != null)
            {
                var serviciosPorCoordenada = context.Servicios.AsQueryable();
                serviciosPorCoordenada = serviciosPorCoordenada
                    .Where(x => x.Coordenadas.Y >= busquedaParams.LatitudSurOeste
                                && x.Coordenadas.X >= busquedaParams.LongitudSurOeste
                                && x.Coordenadas.Y <= busquedaParams.LatitudNorEste
                                && x.Coordenadas.X <= busquedaParams.LongitudNorEste);

                if (busquedaParams.Latitud != null && busquedaParams.Longitud != null)
                {
                    serviciosPorCoordenada = serviciosPorCoordenada.Where(x => x.Coordenadas != null);
                }

                return serviciosPorCoordenada.ToList();
            }

            return new List<Data.Servicio>();
        }

        private List<Data.Servicio> BuildQueryCodigoPostal(BusquedaParams busquedaParams)
        {
            if (!string.IsNullOrEmpty(busquedaParams.CodigoPostal))
            {
                var serviciosPorCodigoPostal = context.Servicios.AsQueryable();
                var codigoPostal = busquedaParams.CodigoPostal;

                serviciosPorCodigoPostal = serviciosPorCodigoPostal.Where(x => x.CodigoPostal == codigoPostal);

                return serviciosPorCodigoPostal.ToList();
            }

            return new List<Data.Servicio>();
        }

        private List<Data.Servicio> BuildQueryLocalidad(BusquedaParams busquedaParams)
        {
            if (!string.IsNullOrEmpty(busquedaParams.Poblacion))
            {
                return context.Servicios
                    .Where(x => x.Localidad != null && EF.Functions.Collate(x.Localidad, "Modern_Spanish_CI_AI").Contains(busquedaParams.Poblacion))
                    .ToList();
            }

            return new List<Data.Servicio>();
        }
    }
}
