﻿using IdentityModel.Client;
using System.Collections.Generic;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Authentication
{
    public partial class ScopeTokenClient : IScopeTokenClient
    {
        private TokenRequest GetTokenRequest(DiscoveryDocumentResponse discovery, Dictionary<string, string> parameters)
        {
            // TODO: Leer las credenciales de appsettings, de algún contener seguro o dejarlas aquí hardcoded...
            return new TokenRequest
            {
                Address = discovery.TokenEndpoint,
                ClientId = "PublicAppGateway",
                ClientSecret = "secret",
                GrantType = "delegation_token",
                Parameters = parameters,
            };
        }
    }
}
