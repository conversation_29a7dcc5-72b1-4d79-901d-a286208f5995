/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class HealthChecksExtensions
    {
        public static void AddHealthChecksGen(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddHealthChecks()
                .AddHealthChecksCustom(configuration);
        }

        public static void MapHealthChecksGen(this IEndpointRouteBuilder endpoints, IConfiguration configuration)
        {
            endpoints.MapHealthChecks("/healthz", new HealthCheckOptions
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            endpoints.MapHealthChecksCustom(configuration);
        }
    }
}