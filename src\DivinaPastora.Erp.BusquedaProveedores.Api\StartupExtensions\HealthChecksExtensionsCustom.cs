using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public static class HealthChecksExtensionsCustom
    {
        public static void AddHealthChecksCustom(this IHealthChecksBuilder healthChecks, IConfiguration configuration)
        {
            // TODO: A completar por el usuario...
        }

        public static void MapHealthChecksCustom(this IEndpointRouteBuilder endpoints, IConfiguration configuration)
        {
            // TODO: A completar por el usuario...
        }
    }
}