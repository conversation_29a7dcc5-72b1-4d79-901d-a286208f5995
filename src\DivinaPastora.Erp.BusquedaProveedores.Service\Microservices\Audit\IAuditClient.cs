﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Model.Audit;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Service.Microservices.Audit
{
    public interface IAuditClient
    {
        Task<bool> AuditRequest(ResultExecutedContext context);

        Task<bool> InsertAudit(string user, string controllerName, string actionName, Request request, string response);
    }
}
