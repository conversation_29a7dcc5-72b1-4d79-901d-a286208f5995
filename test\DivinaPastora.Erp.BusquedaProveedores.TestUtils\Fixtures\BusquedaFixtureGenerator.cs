﻿using DivinaPastora.Erp.BusquedaProveedores.Model;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Data = DivinaPastora.Erp.BusquedaProveedores.Repository.Data;

namespace DivinaPastora.Erp.BusquedaProveedores.TestUtils.Fixtures
{
    public static class BusquedaFixtureGenerator
    {
        public static async Task<List<Cuadro>> GetCuadros()
        {
            return new List<Cuadro>
            {
                new Cuadro
                {
                    Id = 1,
                    NombreId = 1234
                }
            };
        }

        public static async Task<List<Categoria>> GetCategorias()
        {
            return new List<Categoria>
            {
                new Categoria
                {
                    Id = 1,
                    CuadroId = 1,
                    NombreId = 1235
                },
                new Categoria
                {
                    Id = 2,
                    CuadroId = 1,
                    NombreId = 1236
                }
            };
        }

        public static async Task<List<Data.Servicio>> GetServicios()
        {
            return new List<Data.Servicio>
            {
                new Data.Servicio
                {
                    Id = 1,
                    CuadroId = 1,
                    ProductoId = 105,
                    CategoriaId = 1,
                    Nombre = "Servicio1",
                    Provincia = "Provincia1",
                    Localidad = "Localidad1",
                    CodigoPostal = "123456",
                    Ruta = "Ruta1",
                    Coordenadas = new NetTopologySuite.Geometries.Point(1, 1),
                    Prioridad = 0
                },
                new Data.Servicio
                {
                    Id = 2,
                    CuadroId = 1,
                    ProductoId = 105,
                    CategoriaId = 1,
                    Nombre = "Servicio2",
                    Provincia = "Provincia2",
                    Localidad = "Localidad2",
                    CodigoPostal = "56789",
                    Ruta = "Ruta2",
                    Coordenadas = new NetTopologySuite.Geometries.Point(2, 2),
                    Prioridad = 0
                }
            };
        }
    }
}
