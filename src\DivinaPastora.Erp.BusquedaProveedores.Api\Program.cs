/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using DivinaPastora.Erp.BusquedaProveedores.Api.Binders;
using DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares;
using DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NLog;
using NLog.Web;
using System;

var logger = LogManager.Setup().LoadConfigurationFromFile("./nlog.config").GetCurrentClassLogger();

try
{
    logger.Info("Initializing service...");

    var builder = WebApplication.CreateBuilder(args);
    var services = builder.Services;
    var configuration = builder.Configuration;
    var environment = builder.Environment;

    services.AddControllers(opt =>
    {
        opt.ModelBinderProviders.Insert(0, new DefaultBinderProvider());
        opt.ModelBinderProviders.Insert(1, new CustomBinderProvider());
    }).AddNewtonsoftJson(NewtonsoftJsonConfigurationCustom.Configure())
      .ConfigureCustomMvcBuilderBehaviors(logger);

    services.AddSwagger(configuration, environment);
    services.AddAutoMapper();
    services.AddHealthChecksGen(configuration);

    builder.Logging.ClearProviders();
    builder.Host.UseNLog();

    services.AddDependencies(environment);
    services.AddDependenciesCustom(configuration, environment);

    var app = builder.Build();
    app.UseRouting();

    app.UseMiddleware<ExceptionMiddleware>();

    app.UseDefaultStaticFiles(environment);

    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllers();
        endpoints.MapHealthChecksGen(configuration);
    });

    app.UseSwagger(configuration, environment);

    if (environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }

    app.Run();
}
catch (Exception exception)
{
    logger.Error(exception, "Stopped service because of exception");
    throw;
}
finally
{
    LogManager.Shutdown();
}