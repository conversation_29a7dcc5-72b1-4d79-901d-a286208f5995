/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace DivinaPastora.Erp.BusquedaProveedores.Model
{
    public partial class Categoria
    {
        public int Id { get; set; }

        public int CuadroId { get; set; }

        public int NombreId { get; set; }

        public Categoria()
        {
        }

        public Categoria(int id, int cuadroId, int nombreId)
        {
            Id = id;
            CuadroId = cuadroId;
            NombreId = nombreId;
        }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            return obj.GetType() == GetType() && Equals((Categoria)obj);
        }

        public bool Equals(Categoria other)
        {
            if (other is null)
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }
#pragma warning disable SA1119 // Statement should not use unnecessary parenthesis
            return
                (Id == other.Id || (Id != null && Id.Equals(other.Id))) &&
                (CuadroId == other.CuadroId || (CuadroId != null && CuadroId.Equals(other.CuadroId))) &&
                (NombreId == other.NombreId || (NombreId != null && NombreId.Equals(other.NombreId)));
#pragma warning restore SA1119 // Statement should not use unnecessary parenthesis
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = 41;

                if (Id != null)
                {
                    hashCode = (hashCode * 59) + Id.GetHashCode();
                }

                if (CuadroId != null)
                {
                    hashCode = (hashCode * 59) + CuadroId.GetHashCode();
                }

                if (NombreId != null)
                {
                    hashCode = (hashCode * 59) + NombreId.GetHashCode();
                }

                return hashCode;
            }
        }

        public static bool operator ==(Categoria left, Categoria right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(Categoria left, Categoria right)
        {
            return !Equals(left, right);
        }
    }
}
