using Microsoft.AspNetCore.Mvc;
using System;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions
{
    public class NewtonsoftJsonConfigurationCustom
    {
        public static Action<MvcNewtonsoftJsonOptions> Configure()
        {
            return opts =>
            {
                // TODO: A completar por el usuario...
                /* Ej.
                opts.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                opts.SerializerSettings.TypeNameHandling = TypeNameHandling.Auto;
                */
            };
        }
    }
}