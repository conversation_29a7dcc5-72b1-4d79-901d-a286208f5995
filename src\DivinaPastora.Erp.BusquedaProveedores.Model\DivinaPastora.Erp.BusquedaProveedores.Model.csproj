<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Description>DivinaPastora.Erp.BusquedaProveedores.Model</Description>
    <Copyright>DivinaPastora.Erp.BusquedaProveedores.Model</Copyright>
    <TargetFramework>net6.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>DivinaPastora.Erp.BusquedaProveedores.Model</AssemblyName>
    <PackageId>DivinaPastora.Erp.BusquedaProveedores.Model</PackageId>
  </PropertyGroup>
  <PropertyGroup>
    <CodeAnalysisRuleSet>..\..\StyleCopRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <AdditionalFiles Include="..\..\stylecop.json" Link="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
  <PackageReference Include="DivinaPastora.Libs.ProblemDetails.Model" Version="0.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" />
  </ItemGroup>
</Project>
