﻿using DivinaPastora.Erp.BusquedaProveedores.Model;
using Microsoft.EntityFrameworkCore;

namespace DivinaPastora.Erp.BusquedaProveedores.Repository
{
    public class BusquedaProveedoresContext : DbContext
    {
        private const string Schema = "busqueda";
        private const string CuadrosViewName = "CuadroServicios_Cuadros";
        private const string CategoriasViewName = "CuadroServicios_Categorias";
        private const string ServiciosViewName = "CuadroServicios";

        public BusquedaProveedoresContext(DbContextOptions<BusquedaProveedoresContext> options)
           : base(options)
        {
        }

        public DbSet<Cuadro> Cuadros { get; set; }

        public DbSet<Categoria> Categorias { get; set; }

        public DbSet<Data.Servicio> Servicios { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Cuadro>(entity =>
            {
                entity.ToView(CuadrosViewName, Schema);
                entity.HasKey(x => x.Id);
            });

            modelBuilder.Entity<Categoria>(entity =>
            {
                entity.ToView(CategoriasViewName, Schema);
                entity.HasKey(x => x.Id);
                entity.HasIndex(x => new { x.CuadroId });
            });

            modelBuilder.Entity<Data.Servicio>(entity =>
            {
                entity.ToView(ServiciosViewName, Schema);
                entity.HasKey(x => x.Id);
                entity.HasIndex(x => new { x.CuadroId, x.ProductoId, x.CategoriaId });
                entity.Ignore(x => x.Distancia);
            });
        }
    }
}
