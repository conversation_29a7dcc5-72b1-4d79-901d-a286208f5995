﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System.Collections.Generic;

namespace DivinaPastora.Erp.BusquedaProveedores.Model.Authentication
{
    public partial class AuthenticationData : IAuthenticationData
    {
        private readonly bool isProduction;

        public string Token { get; set; }

        public string UserName { get; set; }

        public string ProcessId { get; set; }

        public List<string> Roles { get; set; }

        public AuthenticationData(bool isProduction)
        {
            this.isProduction = isProduction;
        }
    }
}
