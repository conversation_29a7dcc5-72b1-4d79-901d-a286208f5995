/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares;
using DivinaPastora.Erp.BusquedaProveedores.Api.StartupExtensions;
using DivinaPastora.Erp.BusquedaProveedores.Repository;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Serialization;
using SecureConnection.Models;

namespace DivinaPastora.Erp.BusquedaProveedores.Api
{
    public class Startup
    {
        private readonly IWebHostEnvironment hostingEnv;

        private IConfiguration Configuration { get; }

        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            hostingEnv = env;
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers()
                .AddNewtonsoftJson(opts =>
                {
                    opts.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                    opts.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                });
            services.AddSwagger(Configuration, hostingEnv);
            services.AddAutoMapper();

            services.AddDependencies(hostingEnv);
            services.AddDependenciesCustom(Configuration, hostingEnv);

            services.AddClientFactoryCustom(Configuration);
            services.AddDbContext<BusquedaProveedoresContext>(options => options.UseSqlServer(GetConnectionString(), x => x.UseNetTopologySuite()));
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            app.UseRouting();

            app.UseMiddleware<ExceptionMiddleware>();

            app.UseDefaultStaticFiles(env);

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            app.UseSwagger(Configuration, env);

            if (!env.IsProduction())
            {
                app.UseDeveloperExceptionPage();
            }
        }

        private string GetConnectionString()
        {
            string serialNumber = Configuration.GetSection("Connection").GetValue<string>("SerialNumber");
            string filePath = Configuration.GetSection("Connection").GetValue<string>("FilePath");
            string template = Configuration.GetSection("Connection").GetValue<string>("Template");
            if (template.Contains("%"))
            {
                SecureConnection.SecureConnection secureCon = new SecureConnection.SecureConnection();
                DataInfo connectionData = secureCon.ReadContentFile(filePath, serialNumber);
                return template.Replace("%user%", connectionData.User).Replace("%password%", connectionData.Password);
            }

            return template;
        }
    }
}
