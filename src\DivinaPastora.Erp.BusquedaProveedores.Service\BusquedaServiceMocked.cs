/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */
using DivinaPastora.Erp.BusquedaProveedores.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Service
{
    public partial class BusquedaServiceMocked : IBusquedaService
    {
        public Task<ResultadoBusqueda> Busqueda(int cuadroId, int productoId, int categoriaId, int? limit, int? offset, string nombre, string provincia, string poblacion, string codigoPostal, string direccion, double? latitudSurOeste, double? longitudSurOeste, double? latitudNorEste, double? longitudNorEste, double? latitud, double? longitud)
        {
            string exampleJson = null;
            exampleJson = "{\r\n  \"servicios\" : [ {\r\n    \"id\" : 2,\r\n    \"cuadroId\" : 1,\r\n    \"categoriaId\" : 13,\r\n    \"nombre\" : \"Ejemplo de Servicio\",\r\n    \"provincia\" : \"Valencia\",\r\n    \"localidad\" : \"Castellón\",\r\n    \"codigoPostal\" : \"45005\",\r\n    \"ruta\" : \"Calle de ejemplo\",\r\n    \"numero\" : \"16 bis\",\r\n    \"datosAdicionales\" : \"puerta 7\",\r\n    \"telefono\" : \"931111111\",\r\n    \"latitud\" : 41.48737,\r\n    \"longitud\" : 2.03991,\r\n    \"distancia\" : 12313.43,\r\n    \"prioridad\" : 0\r\n  }, {\r\n    \"id\" : 2,\r\n    \"cuadroId\" : 1,\r\n    \"categoriaId\" : 13,\r\n    \"nombre\" : \"Ejemplo de Servicio\",\r\n    \"provincia\" : \"Valencia\",\r\n    \"localidad\" : \"Castellón\",\r\n    \"codigoPostal\" : \"45005\",\r\n    \"ruta\" : \"Calle de ejemplo\",\r\n    \"numero\" : \"16 bis\",\r\n    \"datosAdicionales\" : \"puerta 7\",\r\n    \"telefono\" : \"931111111\",\r\n    \"latitud\" : 41.48737,\r\n    \"longitud\" : 2.03991,\r\n    \"distancia\" : 12313.43,\r\n    \"prioridad\" : 0\r\n  } ],\r\n  \"totalServicios\" : 0\r\n}";

            var example = exampleJson != null
                ? JsonConvert.DeserializeObject<ResultadoBusqueda>(exampleJson)
                : default(ResultadoBusqueda);
            return Task.FromResult(example);
        }

        public Task<List<Categoria>> GetCategorias(int cuadroId)
        {
            string exampleJson = null;
            exampleJson = "[ {\r\n  \"id\" : 2,\r\n  \"cuadroId\" : 1,\r\n  \"nombreId\" : 456\r\n}, {\r\n  \"id\" : 2,\r\n  \"cuadroId\" : 1,\r\n  \"nombreId\" : 456\r\n} ]";

            var example = exampleJson != null
                ? JsonConvert.DeserializeObject<List<Categoria>>(exampleJson)
                : default(List<Categoria>);
            return Task.FromResult(example);
        }

        public Task<List<Cuadro>> GetCuadros()
        {
            string exampleJson = null;
            exampleJson = "[ {\r\n  \"id\" : 1,\r\n  \"nombreId\" : 123\r\n}, {\r\n  \"id\" : 1,\r\n  \"nombreId\" : 123\r\n} ]";

            var example = exampleJson != null
                ? JsonConvert.DeserializeObject<List<Cuadro>>(exampleJson)
                : default(List<Cuadro>);
            return Task.FromResult(example);
        }
    }
}
