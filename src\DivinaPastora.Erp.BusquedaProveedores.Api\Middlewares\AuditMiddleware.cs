﻿/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace DivinaPastora.Erp.BusquedaProveedores.Api.Middlewares
{
    public class AuditMiddleware
    {
        private readonly RequestDelegate next;

        public AuditMiddleware(RequestDelegate next)
        {
            this.next = next;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            httpContext.Request.EnableBuffering();
            await next(httpContext);
        }
    }
}
