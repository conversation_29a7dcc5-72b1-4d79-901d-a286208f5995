﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.26114.2
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{7C9568A9-DC45-404F-8048-7E999DDCB56E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{DA37B1E5-661F-4FA1-91A8-5E0381A36D3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Api", "src\DivinaPastora.Erp.BusquedaProveedores.Api\DivinaPastora.Erp.BusquedaProveedores.Api.csproj", "{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Model", "src\DivinaPastora.Erp.BusquedaProveedores.Model\DivinaPastora.Erp.BusquedaProveedores.Model.csproj", "{3FC254F4-129A-4C76-B1F8-C40BB96BE046}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Service", "src\DivinaPastora.Erp.BusquedaProveedores.Service\DivinaPastora.Erp.BusquedaProveedores.Service.csproj", "{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Api.Test", "test\DivinaPastora.Erp.BusquedaProveedores.Api.Test\DivinaPastora.Erp.BusquedaProveedores.Api.Test.csproj", "{9159AD4F-EC53-4183-8B8C-4A9395A5884B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Service.Test", "test\DivinaPastora.Erp.BusquedaProveedores.Service.Test\DivinaPastora.Erp.BusquedaProveedores.Service.Test.csproj", "{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.TestUtils", "test\DivinaPastora.Erp.BusquedaProveedores.TestUtils\DivinaPastora.Erp.BusquedaProveedores.TestUtils.csproj", "{659AE222-E4A6-4D75-A4BB-9CED743E909B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D240EA54-10B6-4E2A-A715-A46126972330}"
	ProjectSection(SolutionItems) = preProject
		stylecop.json = stylecop.json
		StyleCopRules.ruleset = StyleCopRules.ruleset
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DivinaPastora.Erp.BusquedaProveedores.Repository", "src\DivinaPastora.Erp.BusquedaProveedores.Repository\DivinaPastora.Erp.BusquedaProveedores.Repository.csproj", "{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FC254F4-129A-4C76-B1F8-C40BB96BE046}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FC254F4-129A-4C76-B1F8-C40BB96BE046}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FC254F4-129A-4C76-B1F8-C40BB96BE046}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FC254F4-129A-4C76-B1F8-C40BB96BE046}.Release|Any CPU.Build.0 = Release|Any CPU
		{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18}.Release|Any CPU.Build.0 = Release|Any CPU
		{9159AD4F-EC53-4183-8B8C-4A9395A5884B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9159AD4F-EC53-4183-8B8C-4A9395A5884B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9159AD4F-EC53-4183-8B8C-4A9395A5884B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9159AD4F-EC53-4183-8B8C-4A9395A5884B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{659AE222-E4A6-4D75-A4BB-9CED743E909B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{659AE222-E4A6-4D75-A4BB-9CED743E909B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{659AE222-E4A6-4D75-A4BB-9CED743E909B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{659AE222-E4A6-4D75-A4BB-9CED743E909B}.Release|Any CPU.Build.0 = Release|Any CPU
		{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{AA1D2F11-AB12-4D13-BA29-6B5B3AD0C054} = {7C9568A9-DC45-404F-8048-7E999DDCB56E}
		{3FC254F4-129A-4C76-B1F8-C40BB96BE046} = {7C9568A9-DC45-404F-8048-7E999DDCB56E}
		{D927A9EE-5BD3-4C5C-B24D-A7FE5F081B18} = {7C9568A9-DC45-404F-8048-7E999DDCB56E}
		{9159AD4F-EC53-4183-8B8C-4A9395A5884B} = {DA37B1E5-661F-4FA1-91A8-5E0381A36D3D}
		{8A82F82A-CBAA-49C1-92CB-D23FF759FF2F} = {DA37B1E5-661F-4FA1-91A8-5E0381A36D3D}
		{659AE222-E4A6-4D75-A4BB-9CED743E909B} = {DA37B1E5-661F-4FA1-91A8-5E0381A36D3D}
		{00D4B0F4-B5EE-4403-AF96-CB9103F50E4F} = {7C9568A9-DC45-404F-8048-7E999DDCB56E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2943D937-2B6B-4219-80C3-F351455E0EF9}
	EndGlobalSection
EndGlobal
