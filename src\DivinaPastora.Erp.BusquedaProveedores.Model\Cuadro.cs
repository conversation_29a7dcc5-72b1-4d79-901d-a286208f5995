/*
 * Busqueda Proveedores
 * Archivo autogenerado con Codegen, no modificar manualmente.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace DivinaPastora.Erp.BusquedaProveedores.Model
{
    public partial class Cuadro
    {
        public int Id { get; set; }

        public int NombreId { get; set; }

        public Cuadro()
        {
        }

        public Cuadro(int id, int nombreId)
        {
            Id = id;
            NombreId = nombreId;
        }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            return obj.GetType() == GetType() && Equals((Cuadro)obj);
        }

        public bool Equals(Cuadro other)
        {
            if (other is null)
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }
#pragma warning disable SA1119 // Statement should not use unnecessary parenthesis
            return
                (Id == other.Id || (Id != null && Id.Equals(other.Id))) &&
                (NombreId == other.NombreId || (NombreId != null && NombreId.Equals(other.NombreId)));
#pragma warning restore SA1119 // Statement should not use unnecessary parenthesis
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = 41;

                if (Id != null)
                {
                    hashCode = (hashCode * 59) + Id.GetHashCode();
                }

                if (NombreId != null)
                {
                    hashCode = (hashCode * 59) + NombreId.GetHashCode();
                }

                return hashCode;
            }
        }

        public static bool operator ==(Cuadro left, Cuadro right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(Cuadro left, Cuadro right)
        {
            return !Equals(left, right);
        }
    }
}
